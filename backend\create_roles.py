#!/usr/bin/env python
"""
Create basic roles for testing
"""
import os
import sys
import django

# Add the backend directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'yasmeen_ai.settings')
django.setup()

from users.models.main import UserRole

def create_roles():
    """Create basic roles"""
    
    roles_data = [
        {
            'name': 'user',
            'display_name': 'Regular User',
            'description': 'Basic user with limited access',
            'permissions': ['view_profile', 'edit_profile'],
            'is_active': True
        },
        {
            'name': 'entrepreneur',
            'display_name': 'Entrepreneur',
            'description': 'Business entrepreneur with access to business features',
            'permissions': ['view_profile', 'edit_profile', 'create_business_plan', 'access_ai_chat'],
            'is_active': True
        },
        {
            'name': 'mentor',
            'display_name': '<PERSON><PERSON>',
            'description': 'Mentor with access to mentorship features',
            'permissions': ['view_profile', 'edit_profile', 'mentor_users', 'access_ai_chat'],
            'is_active': True
        },
        {
            'name': 'investor',
            'display_name': 'Investor',
            'description': 'Investor with access to investment features',
            'permissions': ['view_profile', 'edit_profile', 'view_investments', 'access_ai_chat'],
            'is_active': True
        },
        {
            'name': 'moderator',
            'display_name': 'Moderator',
            'description': 'Content moderator with moderation privileges',
            'permissions': ['view_profile', 'edit_profile', 'moderate_content', 'access_ai_chat'],
            'is_active': True
        },
        {
            'name': 'admin',
            'display_name': 'Administrator',
            'description': 'Administrator with elevated privileges',
            'permissions': ['view_profile', 'edit_profile', 'manage_users', 'access_ai_chat', 'admin_access'],
            'is_active': True
        },
        {
            'name': 'super_admin',
            'display_name': 'Super Administrator',
            'description': 'Super administrator with full system access',
            'permissions': ['view_profile', 'edit_profile', 'manage_users', 'access_ai_chat', 'admin_access', 'super_admin_access'],
            'is_active': True
        }
    ]
    
    created_roles = []
    
    for role_data in roles_data:
        try:
            role, created = UserRole.objects.get_or_create(
                name=role_data['name'],
                defaults={
                    'display_name': role_data['display_name'],
                    'description': role_data['description'],
                    'permissions': role_data['permissions'],
                    'is_active': role_data['is_active']
                }
            )
            
            status = "CREATED" if created else "EXISTS"
            created_roles.append({
                'name': role_data['name'],
                'display_name': role_data['display_name'],
                'status': status
            })
            
            print(f"[{status}] {role_data['name']} - {role_data['display_name']}")
            
        except Exception as e:
            print(f"[ERROR] Failed to create role {role_data['name']}: {e}")
    
    return created_roles

def main():
    print("Creating basic roles...")
    print("="*50)
    
    try:
        created_roles = create_roles()
        
        print("\n" + "="*50)
        print("ROLES CREATED SUCCESSFULLY!")
        print("="*50)
        
        for role in created_roles:
            print(f"Role: {role['name']}")
            print(f"Display Name: {role['display_name']}")
            print(f"Status: {role['status']}")
            print("-" * 30)
        
        print("\nRoles are now available for user assignment!")
        
    except Exception as e:
        print(f"[ERROR] Failed to create roles: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()
