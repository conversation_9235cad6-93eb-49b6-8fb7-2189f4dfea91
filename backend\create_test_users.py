#!/usr/bin/env python
"""
Create test users for all roles to test the application
"""
import os
import sys
import django

# Add the backend directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'yasmeen_ai.settings')
django.setup()

from django.contrib.auth.models import User
from users.models.main import UserProfile, UserRole, UserRoleAssignment

def create_test_users():
    """Create test users for all roles"""
    
    # Test users data
    test_users = [
        {
            'username': 'testuser',
            'password': 'password123',
            'email': '<EMAIL>',
            'first_name': 'Test',
            'last_name': 'User',
            'role': 'user',
            'bio': 'Regular test user'
        },
        {
            'username': 'entrepreneur1',
            'password': 'password123',
            'email': '<EMAIL>',
            'first_name': '<PERSON>',
            'last_name': 'Entrepreneur',
            'role': 'entrepreneur',
            'bio': 'Test entrepreneur user'
        },
        {
            'username': 'mentor1',
            'password': 'password123',
            'email': '<EMAIL>',
            'first_name': 'Sara',
            'last_name': 'Mentor',
            'role': 'mentor',
            'bio': 'Test mentor user'
        },
        {
            'username': 'investor1',
            'password': 'password123',
            'email': '<EMAIL>',
            'first_name': 'Omar',
            'last_name': 'Investor',
            'role': 'investor',
            'bio': 'Test investor user'
        },
        {
            'username': 'moderator1',
            'password': 'password123',
            'email': '<EMAIL>',
            'first_name': 'Layla',
            'last_name': 'Moderator',
            'role': 'moderator',
            'bio': 'Test moderator user'
        },
        {
            'username': 'admin1',
            'password': 'password123',
            'email': '<EMAIL>',
            'first_name': 'Khalid',
            'last_name': 'Admin',
            'role': 'admin',
            'bio': 'Test admin user'
        },
        {
            'username': 'superadmin',
            'password': 'superadmin123',
            'email': '<EMAIL>',
            'first_name': 'Super',
            'last_name': 'Admin',
            'role': 'super_admin',
            'bio': 'Super Administrator - Full System Access'
        }
    ]
    
    created_users = []
    
    for user_data in test_users:
        try:
            # Create or update user
            user, user_created = User.objects.get_or_create(
                username=user_data['username'],
                defaults={
                    'email': user_data['email'],
                    'first_name': user_data['first_name'],
                    'last_name': user_data['last_name'],
                    'is_staff': user_data['role'] in ['admin', 'super_admin'],
                    'is_superuser': user_data['role'] == 'super_admin'
                }
            )
            
            # Set password
            user.set_password(user_data['password'])
            user.save()
            
            # Create or update user profile
            profile, profile_created = UserProfile.objects.get_or_create(
                user=user,
                defaults={
                    'bio': user_data['bio'],
                    'location': 'Damascus, Syria',
                    'language': 'ar'
                }
            )
            
            # Get role
            try:
                role = UserRole.objects.get(name=user_data['role'])
            except UserRole.DoesNotExist:
                print(f"[WARNING] Role '{user_data['role']}' not found for user {user_data['username']}")
                continue
            
            # Assign role
            role_assignment, assignment_created = UserRoleAssignment.objects.get_or_create(
                user_profile=profile,
                role=role,
                defaults={
                    'assigned_by': user,
                    'is_active': True,
                    'is_approved': True
                }
            )
            
            status = "CREATED" if user_created else "UPDATED"
            created_users.append({
                'username': user_data['username'],
                'password': user_data['password'],
                'role': user_data['role'],
                'status': status
            })
            
            print(f"[{status}] {user_data['username']} ({user_data['role']})")
            
        except Exception as e:
            print(f"[ERROR] Failed to create user {user_data['username']}: {e}")
    
    return created_users

def main():
    print("Creating test users for all roles...")
    print("="*50)
    
    try:
        created_users = create_test_users()
        
        print("\n" + "="*50)
        print("TEST USERS CREATED SUCCESSFULLY!")
        print("="*50)
        
        for user in created_users:
            print(f"Username: {user['username']}")
            print(f"Password: {user['password']}")
            print(f"Role: {user['role']}")
            print(f"Status: {user['status']}")
            print("-" * 30)
        
        print("\nYou can now test all roles with these credentials!")
        
    except Exception as e:
        print(f"[ERROR] Failed to create test users: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()
