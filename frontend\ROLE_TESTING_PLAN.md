# 🧪 COMPREHENSIVE ROLE TESTING PLAN

## Overview
Testing all 7 roles in the system to verify login redirects, access controls, and navigation work correctly.

## Roles to Test
1. **user** - Regular user (minimal access)
2. **entrepreneur** - Business role
3. **mentor** - Mentorship role  
4. **investor** - Investment role
5. **moderator** - Content moderation role
6. **admin** - Administrative role
7. **super_admin** - Super administrator role

---

## Test Cases for Each Role

### 1. 👤 REGULAR USER (`user`)
**Expected Behavior:**
- ✅ Login redirect: `/user/home`
- ✅ Layout: UserLayout (no sidebar)
- ✅ Navigation: Minimal (Home, Profile, Settings, Events, Resources)
- ❌ NO access to: Dashboard, AI Chat, Business features, Analytics

**Test Steps:**
1. Login with user credentials
2. Verify redirect to `/user/home`
3. Check no sidebar is shown
4. Verify access to: `/user/home`, `/user/profile`, `/user/settings`
5. Verify blocked from: `/user/dashboard`, `/user/ai-chat`, `/entrepreneur/dashboard`

**Expected Pages:**
- ✅ `/user/home` - UserHomePage
- ✅ `/user/profile` - UserProfilePage  
- ✅ `/user/settings` - SettingsPage
- ❌ `/user/dashboard` - Should redirect to `/user/home`
- ❌ `/user/ai-chat` - Should be blocked

---

### 2. 🚀 ENTREPRENEUR (`entrepreneur`)
**Expected Behavior:**
- ✅ Login redirect: `/entrepreneur/dashboard`
- ✅ Layout: AuthenticatedLayout (with sidebar)
- ✅ Navigation: Business features, AI Chat, Analytics
- ✅ Access to: Dashboard, Business Plans, Funding, Mentorship, Templates

**Test Steps:**
1. Login with entrepreneur credentials
2. Verify redirect to `/entrepreneur/dashboard`
3. Check sidebar is shown with business navigation
4. Verify access to business features
5. Test AI chat access

**Expected Pages:**
- ✅ `/entrepreneur/dashboard` - DashboardPage
- ✅ `/entrepreneur/ai-chat` - AIChatPage
- ✅ `/entrepreneur/profile` - UserProfilePage
- ✅ `/entrepreneur/settings` - SettingsPage
- ✅ `/entrepreneur/business-plans` - Business features
- ✅ `/entrepreneur/funding` - Funding features

---

### 3. 🎯 MENTOR (`mentor`)
**Expected Behavior:**
- ✅ Login redirect: `/mentor/dashboard`
- ✅ Layout: AuthenticatedLayout (with sidebar)
- ✅ Navigation: Mentorship features, Sessions, Mentees
- ✅ Access to: Dashboard, AI Chat, Mentorship management

**Test Steps:**
1. Login with mentor credentials
2. Verify redirect to `/mentor/dashboard`
3. Check mentor-specific navigation
4. Test mentorship features

**Expected Pages:**
- ✅ `/mentor/dashboard` - DashboardPage
- ✅ `/mentor/ai-chat` - AIChatPage
- ✅ `/mentor/mentees` - Mentee management
- ✅ `/mentor/sessions` - Session management
- ✅ `/mentor/mentorship` - Mentorship features

---

### 4. 💰 INVESTOR (`investor`)
**Expected Behavior:**
- ✅ Login redirect: `/investor/dashboard`
- ✅ Layout: AuthenticatedLayout (with sidebar)
- ✅ Navigation: Investment features, Portfolio, Opportunities
- ✅ Access to: Dashboard, AI Chat, Investment management

**Test Steps:**
1. Login with investor credentials
2. Verify redirect to `/investor/dashboard`
3. Check investor-specific navigation
4. Test investment features

**Expected Pages:**
- ✅ `/investor/dashboard` - DashboardPage
- ✅ `/investor/ai-chat` - AIChatPage
- ✅ `/investor/portfolio` - Portfolio management
- ✅ `/investor/opportunities` - Investment opportunities
- ✅ `/investor/funding` - Funding features

---

### 5. 🛡️ MODERATOR (`moderator`)
**Expected Behavior:**
- ✅ Login redirect: `/moderator/dashboard`
- ✅ Layout: AuthenticatedLayout (with sidebar)
- ✅ Navigation: Moderation features, Content management
- ✅ Access to: Dashboard, AI Chat, Content moderation

**Test Steps:**
1. Login with moderator credentials
2. Verify redirect to `/moderator/dashboard`
3. Check moderation-specific navigation
4. Test content moderation features

**Expected Pages:**
- ✅ `/moderator/dashboard` - DashboardPage
- ✅ `/moderator/ai-chat` - AIChatPage
- ✅ `/moderator/content-moderation` - Content moderation
- ✅ `/moderator/reports-management` - Reports management

---

### 6. ⚡ ADMIN (`admin`)
**Expected Behavior:**
- ✅ Login redirect: `/admin/dashboard`
- ✅ Layout: AuthenticatedLayout (with sidebar)
- ✅ Navigation: Admin features, User management, Analytics
- ✅ Access to: Dashboard, AI Chat, User management, Content management

**Test Steps:**
1. Login with admin credentials
2. Verify redirect to `/admin/dashboard`
3. Check admin-specific navigation
4. Test admin features

**Expected Pages:**
- ✅ `/admin/dashboard` - DashboardPage
- ✅ `/admin/ai-chat` - AIChatPage
- ✅ `/admin/users` - User management
- ✅ `/admin/content` - Content management
- ✅ `/admin/analytics` - Analytics
- ✅ `/admin/approvals` - User approvals

---

### 7. 👑 SUPER ADMIN (`super_admin`)
**Expected Behavior:**
- ✅ Login redirect: `/super_admin/dashboard`
- ✅ Layout: AuthenticatedLayout (with sidebar)
- ✅ Navigation: All features, System management, Security
- ✅ Access to: Everything including system management

**Test Steps:**
1. Login with super_admin credentials
2. Verify redirect to `/super_admin/dashboard`
3. Check super admin navigation (most comprehensive)
4. Test system management features

**Expected Pages:**
- ✅ `/super_admin/dashboard` - DashboardPage
- ✅ `/super_admin/ai-chat` - AIChatPage
- ✅ `/super_admin/users` - User management
- ✅ `/super_admin/content` - Content management
- ✅ `/super_admin/analytics` - Analytics
- ✅ `/super_admin/approvals` - User approvals
- ✅ `/super_admin/system` - System management
- ✅ `/super_admin/security` - Security center

---

## Cross-Role Access Testing

### Access Control Tests
1. **User trying to access business features** - Should be blocked
2. **Entrepreneur trying to access admin features** - Should be blocked  
3. **Admin trying to access super admin features** - Should be blocked
4. **Invalid role URLs** - Should redirect to appropriate home page

### Navigation Tests
1. **Sidebar visibility** - Only business roles and above should see sidebar
2. **Navigation items** - Each role should see only their allowed navigation
3. **Route protection** - Unauthorized routes should redirect properly

---

## Test Execution Checklist

- [ ] Test user role (minimal access)
- [ ] Test entrepreneur role (business features)
- [ ] Test mentor role (mentorship features)
- [ ] Test investor role (investment features)
- [ ] Test moderator role (moderation features)
- [ ] Test admin role (admin features)
- [ ] Test super_admin role (all features)
- [ ] Test cross-role access restrictions
- [ ] Test invalid role handling
- [ ] Test login redirects for each role
- [ ] Test sidebar navigation for each role
- [ ] Test route protection for each role

---

## Expected Results Summary

| Role | Login Redirect | Sidebar | Dashboard | AI Chat | Admin Features |
|------|---------------|---------|-----------|---------|----------------|
| user | `/user/home` | ❌ No | ❌ No | ❌ No | ❌ No |
| entrepreneur | `/entrepreneur/dashboard` | ✅ Yes | ✅ Yes | ✅ Yes | ❌ No |
| mentor | `/mentor/dashboard` | ✅ Yes | ✅ Yes | ✅ Yes | ❌ No |
| investor | `/investor/dashboard` | ✅ Yes | ✅ Yes | ✅ Yes | ❌ No |
| moderator | `/moderator/dashboard` | ✅ Yes | ✅ Yes | ✅ Yes | ⚠️ Limited |
| admin | `/admin/dashboard` | ✅ Yes | ✅ Yes | ✅ Yes | ✅ Yes |
| super_admin | `/super_admin/dashboard` | ✅ Yes | ✅ Yes | ✅ Yes | ✅ All |
