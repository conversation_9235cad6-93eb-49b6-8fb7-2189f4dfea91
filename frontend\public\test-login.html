<!DOCTYPE html>
<html>
<head>
    <title>Login Test</title>
</head>
<body>
    <h1>Login Test</h1>
    <form id="loginForm">
        <div>
            <label>Username:</label>
            <input type="text" id="username" value="regularuser" />
        </div>
        <div>
            <label>Password:</label>
            <input type="password" id="password" value="testpass123" />
        </div>
        <button type="submit">Login</button>
    </form>
    
    <div id="result"></div>

    <script>
        document.getElementById('loginForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const resultDiv = document.getElementById('result');
            
            try {
                console.log('Attempting login...');
                const response = await fetch('http://localhost:8000/api/auth/login/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ username, password })
                });
                
                console.log('Response status:', response.status);
                const data = await response.json();
                console.log('Response data:', data);
                
                if (response.ok) {
                    resultDiv.innerHTML = '<p style="color: green;">Login successful!</p><pre>' + JSON.stringify(data, null, 2) + '</pre>';
                } else {
                    resultDiv.innerHTML = '<p style="color: red;">Login failed: ' + (data.message || 'Unknown error') + '</p>';
                }
            } catch (error) {
                console.error('Login error:', error);
                resultDiv.innerHTML = '<p style="color: red;">Network error: ' + error.message + '</p>';
            }
        });
    </script>
</body>
</html>