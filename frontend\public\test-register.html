<!DOCTYPE html>
<html>
<head>
    <title>Registration Test</title>
</head>
<body>
    <h1>Registration Test</h1>
    <form id="registerForm">
        <div>
            <label>Username:</label>
            <input type="text" id="username" value="testuser2" />
        </div>
        <div>
            <label>Email:</label>
            <input type="email" id="email" value="<EMAIL>" />
        </div>
        <div>
            <label>First Name:</label>
            <input type="text" id="first_name" value="Test" />
        </div>
        <div>
            <label>Last Name:</label>
            <input type="text" id="last_name" value="User" />
        </div>
        <div>
            <label>Password:</label>
            <input type="password" id="password" value="testpass123" />
        </div>
        <div>
            <label>Confirm Password:</label>
            <input type="password" id="password_confirm" value="testpass123" />
        </div>
        <button type="submit">Register</button>
    </form>
    
    <div id="result"></div>

    <script>
        document.getElementById('registerForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const formData = {
                username: document.getElementById('username').value,
                email: document.getElementById('email').value,
                first_name: document.getElementById('first_name').value,
                last_name: document.getElementById('last_name').value,
                password: document.getElementById('password').value,
                password_confirm: document.getElementById('password_confirm').value,
                phone: '',
                location: '',
                bio: '',
                company: '',
                job_title: '',
                website: '',
                linkedin_url: '',
                language: 'en',
                selected_role: 'user',
                portfolio_url: '',
                motivation: '',
                qualifications: ''
            };
            
            const resultDiv = document.getElementById('result');
            
            try {
                console.log('Attempting registration...');
                console.log('Registration data:', formData);
                
                const response = await fetch('http://localhost:8000/api/auth/register-enhanced/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(formData)
                });
                
                console.log('Response status:', response.status);
                const data = await response.json();
                console.log('Response data:', data);
                
                if (response.ok) {
                    resultDiv.innerHTML = '<p style="color: green;">Registration successful!</p><pre>' + JSON.stringify(data, null, 2) + '</pre>';
                } else {
                    resultDiv.innerHTML = '<p style="color: red;">Registration failed:</p><pre>' + JSON.stringify(data, null, 2) + '</pre>';
                }
            } catch (error) {
                console.error('Registration error:', error);
                resultDiv.innerHTML = '<p style="color: red;">Network error: ' + error.message + '</p>';
            }
        });
    </script>
</body>
</html>