import React, { useEffect, useState } from 'react';
import { useRoles } from '../../hooks/useRoles';
import { useAuth } from '../../hooks/useAuth';
import { useParams } from 'react-router-dom';

const RoleDebugger: React.FC = () => {
  const { role } = useParams<{ role: string }>();
  const { primaryRole, userInfo, isLoading, hasRole } = useRoles();
  const { isAuthenticated } = useAuth();
  const [roleCheckResults, setRoleCheckResults] = useState<Record<string, boolean>>({});

  useEffect(() => {
    const checkRoles = async () => {
      if (!isAuthenticated || isLoading) return;

      const rolesToCheck = ['user', 'admin', 'super_admin', 'superadmin', 'entrepreneur', 'mentor', 'investor', 'moderator'];
      const results: Record<string, boolean> = {};

      for (const roleToCheck of rolesToCheck) {
        try {
          results[roleToCheck] = await hasRole(roleToCheck);
        } catch (error) {
          console.error(`Error checking role ${roleToCheck}:`, error);
          results[roleToCheck] = false;
        }
      }

      setRoleCheckResults(results);
    };

    checkRoles();
  }, [isAuthenticated, isLoading, hasRole]);

  if (!isAuthenticated) {
    return (
      <div className="fixed top-0 left-0 w-full bg-red-500 text-white p-4 z-50">
        <h3 className="font-bold">🔍 Role Debugger - Not Authenticated</h3>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="fixed top-0 left-0 w-full bg-yellow-500 text-white p-4 z-50">
        <h3 className="font-bold">🔍 Role Debugger - Loading...</h3>
      </div>
    );
  }

  return (
    <div className="fixed top-0 left-0 w-full bg-blue-500 text-white p-4 z-50 text-sm">
      <h3 className="font-bold mb-2">🔍 Role Debugger</h3>
      <div className="grid grid-cols-2 gap-4">
        <div>
          <p><strong>URL Role:</strong> {role || 'none'}</p>
          <p><strong>Primary Role:</strong> {primaryRole || 'none'}</p>
          <p><strong>Is Authenticated:</strong> {isAuthenticated ? 'Yes' : 'No'}</p>
          <p><strong>Is Loading:</strong> {isLoading ? 'Yes' : 'No'}</p>
        </div>
        <div>
          <p><strong>User Info:</strong></p>
          <pre className="text-xs bg-blue-600 p-2 rounded">
            {JSON.stringify(userInfo, null, 2)}
          </pre>
        </div>
      </div>
      <div className="mt-2">
        <p><strong>Role Check Results:</strong></p>
        <div className="flex flex-wrap gap-2">
          {Object.entries(roleCheckResults).map(([roleName, hasRoleResult]) => (
            <span
              key={roleName}
              className={`px-2 py-1 rounded text-xs ${
                hasRoleResult ? 'bg-green-600' : 'bg-red-600'
              }`}
            >
              {roleName}: {hasRoleResult ? '✅' : '❌'}
            </span>
          ))}
        </div>
      </div>
    </div>
  );
};

export default RoleDebugger;
