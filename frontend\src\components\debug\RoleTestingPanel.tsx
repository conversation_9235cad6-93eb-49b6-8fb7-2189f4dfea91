/**
 * 🧪 ROLE TESTING PANEL
 * Component to test and verify role-based access controls
 */

import React, { useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '../../hooks/useAuth';
import { useRoles } from '../../hooks/useRoles';
import { 
  User, 
  Shield, 
  Settings, 
  BarChart3, 
  Users, 
  Bot,
  Home,
  CheckCircle,
  XCircle,
  AlertCircle
} from 'lucide-react';

const RoleTestingPanel: React.FC = () => {
  const { user, isAuthenticated } = useAuth();
  const { 
    primaryRole, 
    userRoles, 
    hasRole, 
    hasAnyRole, 
    isAdmin, 
    isSuperAdmin, 
    getDashboardRoute,
    getSidebarNavigation 
  } = useRoles();
  const navigate = useNavigate();
  const location = useLocation();
  const [testResults, setTestResults] = useState<Record<string, boolean>>({});

  const allRoles = ['user', 'entrepreneur', 'mentor', 'investor', 'moderator', 'admin', 'super_admin'];

  const testRoutes = [
    { path: '/user/home', label: 'User Home', expectedRoles: ['user'] },
    { path: '/entrepreneur/dashboard', label: 'Entrepreneur Dashboard', expectedRoles: ['entrepreneur'] },
    { path: '/mentor/dashboard', label: 'Mentor Dashboard', expectedRoles: ['mentor'] },
    { path: '/investor/dashboard', label: 'Investor Dashboard', expectedRoles: ['investor'] },
    { path: '/moderator/dashboard', label: 'Moderator Dashboard', expectedRoles: ['moderator'] },
    { path: '/admin/dashboard', label: 'Admin Dashboard', expectedRoles: ['admin'] },
    { path: '/super_admin/dashboard', label: 'Super Admin Dashboard', expectedRoles: ['super_admin'] },
    { path: '/admin/users', label: 'User Management', expectedRoles: ['admin', 'super_admin'] },
    { path: '/super_admin/system', label: 'System Management', expectedRoles: ['super_admin'] },
  ];

  const testRoleAccess = async () => {
    const results: Record<string, boolean> = {};
    
    for (const role of allRoles) {
      try {
        results[role] = await hasRole(role);
      } catch (error) {
        results[role] = false;
      }
    }
    
    setTestResults(results);
  };

  const testNavigation = async () => {
    try {
      const dashboardRoute = await getDashboardRoute();
      const sidebarNav = getSidebarNavigation();
      
      console.log('🧪 Navigation Test Results:', {
        currentRole: primaryRole,
        dashboardRoute,
        sidebarNavItems: sidebarNav.length,
        sidebarNav: sidebarNav.map(item => ({ id: item.id, name: item.name, path: item.path }))
      });
    } catch (error) {
      console.error('Navigation test failed:', error);
    }
  };

  const navigateToRoute = (path: string) => {
    navigate(path);
  };

  if (!isAuthenticated) {
    return (
      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 m-4">
        <div className="flex items-center">
          <AlertCircle className="w-5 h-5 text-yellow-600 mr-2" />
          <span className="text-yellow-800">Please login to test roles</span>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white border border-gray-200 rounded-lg p-6 m-4 shadow-sm">
      <h2 className="text-xl font-bold text-gray-900 mb-4 flex items-center">
        <Shield className="w-6 h-6 mr-2 text-blue-600" />
        Role Testing Panel
      </h2>

      {/* Current User Info */}
      <div className="bg-blue-50 rounded-lg p-4 mb-6">
        <h3 className="font-semibold text-blue-900 mb-2">Current User Info</h3>
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div>
            <span className="font-medium">Username:</span> {user?.username}
          </div>
          <div>
            <span className="font-medium">Primary Role:</span> 
            <span className={`ml-2 px-2 py-1 rounded text-xs font-medium ${
              primaryRole === 'user' ? 'bg-gray-100 text-gray-800' :
              primaryRole === 'super_admin' ? 'bg-red-100 text-red-800' :
              primaryRole === 'admin' ? 'bg-purple-100 text-purple-800' :
              'bg-blue-100 text-blue-800'
            }`}>
              {primaryRole}
            </span>
          </div>
          <div>
            <span className="font-medium">Current Path:</span> {location.pathname}
          </div>
          <div>
            <span className="font-medium">All Roles:</span> {userRoles.map(r => r.name).join(', ')}
          </div>
        </div>
      </div>

      {/* Role Access Test */}
      <div className="mb-6">
        <div className="flex items-center justify-between mb-3">
          <h3 className="font-semibold text-gray-900">Role Access Test</h3>
          <button
            onClick={testRoleAccess}
            className="px-3 py-1 bg-blue-600 text-white rounded text-sm hover:bg-blue-700"
          >
            Test All Roles
          </button>
        </div>
        
        <div className="grid grid-cols-4 gap-2">
          {allRoles.map(role => (
            <div key={role} className="flex items-center justify-between p-2 border rounded">
              <span className="text-sm font-medium">{role}</span>
              {testResults[role] !== undefined && (
                testResults[role] ? 
                  <CheckCircle className="w-4 h-4 text-green-600" /> :
                  <XCircle className="w-4 h-4 text-red-600" />
              )}
            </div>
          ))}
        </div>
      </div>

      {/* Navigation Test */}
      <div className="mb-6">
        <div className="flex items-center justify-between mb-3">
          <h3 className="font-semibold text-gray-900">Navigation Test</h3>
          <button
            onClick={testNavigation}
            className="px-3 py-1 bg-green-600 text-white rounded text-sm hover:bg-green-700"
          >
            Test Navigation
          </button>
        </div>
        <p className="text-sm text-gray-600">Check browser console for navigation test results</p>
      </div>

      {/* Route Testing */}
      <div className="mb-6">
        <h3 className="font-semibold text-gray-900 mb-3">Route Testing</h3>
        <div className="grid grid-cols-1 gap-2">
          {testRoutes.map(route => {
            const hasAccess = route.expectedRoles.includes(primaryRole);
            return (
              <div key={route.path} className="flex items-center justify-between p-2 border rounded">
                <div className="flex items-center">
                  <span className="text-sm font-medium mr-2">{route.label}</span>
                  <span className="text-xs text-gray-500">({route.path})</span>
                </div>
                <div className="flex items-center space-x-2">
                  {hasAccess ? (
                    <CheckCircle className="w-4 h-4 text-green-600" />
                  ) : (
                    <XCircle className="w-4 h-4 text-red-600" />
                  )}
                  <button
                    onClick={() => navigateToRoute(route.path)}
                    className="px-2 py-1 bg-gray-600 text-white rounded text-xs hover:bg-gray-700"
                  >
                    Test
                  </button>
                </div>
              </div>
            );
          })}
        </div>
      </div>

      {/* Quick Actions */}
      <div>
        <h3 className="font-semibold text-gray-900 mb-3">Quick Actions</h3>
        <div className="flex flex-wrap gap-2">
          <button
            onClick={() => navigate('/dashboard')}
            className="px-3 py-1 bg-blue-600 text-white rounded text-sm hover:bg-blue-700"
          >
            Go to Dashboard
          </button>
          <button
            onClick={() => navigate(`/${primaryRole}/profile`)}
            className="px-3 py-1 bg-green-600 text-white rounded text-sm hover:bg-green-700"
          >
            Go to Profile
          </button>
          <button
            onClick={() => navigate(`/${primaryRole}/settings`)}
            className="px-3 py-1 bg-purple-600 text-white rounded text-sm hover:bg-purple-700"
          >
            Go to Settings
          </button>
          {primaryRole !== 'user' && (
            <button
              onClick={() => navigate(`/${primaryRole}/ai-chat`)}
              className="px-3 py-1 bg-orange-600 text-white rounded text-sm hover:bg-orange-700"
            >
              Go to AI Chat
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default RoleTestingPanel;
