/**
 * 🏠 USER LAYOUT
 * Simple layout for regular users without sidebar
 * Regular users should have a minimal interface
 */

import React from 'react';
import { Outlet } from 'react-router-dom';

interface UserLayoutProps {
  children?: React.ReactNode;
}

/**
 * Simple layout for regular users
 * No sidebar, no complex navigation - just the content
 */
const UserLayout: React.FC<UserLayoutProps> = ({ children }) => {
  // Use Outlet for nested routes, fallback to children for direct usage
  const content = children || <Outlet />;

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Simple content area - no sidebar */}
      <main className="w-full">
        {content}
      </main>
    </div>
  );
};

export default UserLayout;
