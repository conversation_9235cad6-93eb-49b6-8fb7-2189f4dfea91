import React, { useState, useEffect, useReducer, useCallback, useMemo, useRef } from 'react';
import { useAuth } from '../hooks/useAuth';
import { useLanguage } from '../hooks/useLanguage';
import {
  Users,
  MessageCircle,
  Heart,
  Share2,
  Plus,
  Edit3,
  Trash2,
  Send,
  X,
  MoreHorizontal,
  MessageSquare,
  Eye,
  Search,
  Filter,
  TrendingUp,
  Bookmark,
  Globe,
  Clock,
  Zap,
  Flame as Fire,
  Crown,
  CheckCircle as Verified,
  Image as ImageIcon,
  Video,
  Link,
  Hash,
  Bell,
  Settings,
  ChevronDown,
  ChevronUp,
  Reply,
  AtSign,
  Paperclip,
  Smile,
  Bold,
  Italic,
  List,
  Quote,
  Code,
  Maximize2,
  Minimize2,
  Download,
  Upload,
  BarChart3,
  Shield,
  Flag,
  Pin,
  Archive,
  RefreshCw,
  SortAsc,
  SortDesc,
  Calendar,
  MapPin,
  ExternalLink,
  Mic,
  Camera,
  FileText,
  Layers,
  Activity,
  TrendingUp as Trending,
  Star,
  Award,
  Target,
  Lightbulb,
  Rocket,
  Briefcase,
  GraduationCap,
  Coffee,
  Headphones,
  Wifi,
  WifiOff,
  CheckCircle2,
  AlertCircle,
  Info,
  XCircle
} from 'lucide-react';

// ==================== ENTERPRISE-LEVEL TYPE DEFINITIONS ====================

interface User {
  id: string;
  username: string;
  firstName: string;
  lastName: string;
  email: string;
  avatar?: string;
  role: UserRole;
  badge?: UserBadge;
  isVerified: boolean;
  isOnline: boolean;
  lastSeen: Date;
  joinedAt: Date;
  reputation: number;
  followersCount: number;
  followingCount: number;
  postsCount: number;
  bio?: string;
  location?: string;
  website?: string;
  skills: string[];
  interests: string[];
  achievements: Achievement[];
}

interface UserRole {
  id: string;
  name: string;
  level: number;
  permissions: Permission[];
  color: string;
  icon: string;
}

interface UserBadge {
  id: string;
  name: string;
  description: string;
  icon: string;
  color: string;
  rarity: 'common' | 'rare' | 'epic' | 'legendary';
}

interface Achievement {
  id: string;
  name: string;
  description: string;
  icon: string;
  unlockedAt: Date;
  progress?: number;
  maxProgress?: number;
}

interface Permission {
  id: string;
  name: string;
  description: string;
  scope: 'global' | 'community' | 'post' | 'comment';
}

interface CommunityPost {
  id: string;
  author: User;
  title: string;
  content: PostContent;
  category: Category;
  tags: Tag[];
  createdAt: Date;
  updatedAt?: Date;
  scheduledAt?: Date;
  expiresAt?: Date;
  visibility: 'public' | 'private' | 'followers' | 'premium';
  status: 'draft' | 'published' | 'archived' | 'deleted' | 'flagged';
  engagement: PostEngagement;
  media: MediaAttachment[];
  poll?: Poll;
  location?: Location;
  mentions: User[];
  isEdited: boolean;
  editHistory: PostEdit[];
  moderationFlags: ModerationFlag[];
  analytics: PostAnalytics;
  permissions: PostPermissions;
}

interface PostContent {
  raw: string;
  formatted: string;
  excerpt: string;
  wordCount: number;
  readingTime: number;
  language: string;
  sentiment?: 'positive' | 'neutral' | 'negative';
}

interface PostEngagement {
  likes: number;
  dislikes: number;
  comments: number;
  shares: number;
  saves: number;
  views: number;
  uniqueViews: number;
  clickThroughRate: number;
  engagementRate: number;
  userInteractions: {
    isLiked: boolean;
    isDisliked: boolean;
    isSaved: boolean;
    isShared: boolean;
    hasCommented: boolean;
    hasViewed: boolean;
  };
}

interface MediaAttachment {
  id: string;
  type: 'image' | 'video' | 'audio' | 'document' | 'link';
  url: string;
  thumbnailUrl?: string;
  filename: string;
  size: number;
  mimeType: string;
  dimensions?: { width: number; height: number };
  duration?: number;
  metadata: Record<string, any>;
  uploadedAt: Date;
  isProcessing: boolean;
  processingProgress?: number;
}

interface Comment {
  id: string;
  postId: string;
  parentId?: string;
  author: User;
  content: string;
  createdAt: Date;
  updatedAt?: Date;
  isEdited: boolean;
  likes: number;
  dislikes: number;
  replies: Comment[];
  depth: number;
  isDeleted: boolean;
  moderationFlags: ModerationFlag[];
  mentions: User[];
  media: MediaAttachment[];
  userInteractions: {
    isLiked: boolean;
    isDisliked: boolean;
  };
}

interface Category {
  id: string;
  name: string;
  description: string;
  icon: string;
  color: string;
  parentId?: string;
  children: Category[];
  postCount: number;
  isActive: boolean;
  moderators: User[];
  rules: string[];
  tags: Tag[];
}

interface Tag {
  id: string;
  name: string;
  description?: string;
  color: string;
  usageCount: number;
  trending: boolean;
  trendingScore: number;
  createdAt: Date;
  category?: Category;
}

interface Poll {
  id: string;
  question: string;
  options: PollOption[];
  allowMultiple: boolean;
  allowAddOptions: boolean;
  expiresAt?: Date;
  totalVotes: number;
  userVote?: string[];
}

interface PollOption {
  id: string;
  text: string;
  votes: number;
  percentage: number;
}

interface Location {
  id: string;
  name: string;
  coordinates: { lat: number; lng: number };
  address: string;
  city: string;
  country: string;
}

interface PostEdit {
  id: string;
  editedAt: Date;
  editedBy: User;
  changes: {
    field: string;
    oldValue: any;
    newValue: any;
  }[];
  reason?: string;
}

interface ModerationFlag {
  id: string;
  type: 'spam' | 'inappropriate' | 'harassment' | 'misinformation' | 'copyright' | 'other';
  reason: string;
  reportedBy: User;
  reportedAt: Date;
  status: 'pending' | 'reviewed' | 'resolved' | 'dismissed';
  moderator?: User;
  moderatorNotes?: string;
  resolvedAt?: Date;
}

interface PostAnalytics {
  impressions: number;
  reach: number;
  engagement: number;
  clickThroughRate: number;
  shareRate: number;
  saveRate: number;
  commentRate: number;
  demographics: {
    ageGroups: Record<string, number>;
    locations: Record<string, number>;
    devices: Record<string, number>;
    referrers: Record<string, number>;
  };
  timeSeriesData: {
    timestamp: Date;
    views: number;
    likes: number;
    comments: number;
    shares: number;
  }[];
}

interface PostPermissions {
  canEdit: boolean;
  canDelete: boolean;
  canPin: boolean;
  canArchive: boolean;
  canModerate: boolean;
  canViewAnalytics: boolean;
  canExport: boolean;
}

// ==================== STATE MANAGEMENT INTERFACES ====================

interface CommunityState {
  // Data
  posts: CommunityPost[];
  comments: Record<string, Comment[]>;
  users: Record<string, User>;
  categories: Category[];
  tags: Tag[];

  // UI State
  activeView: 'feed' | 'trending' | 'following' | 'saved' | 'analytics';
  selectedCategory: string;
  selectedTags: string[];
  sortBy: 'newest' | 'oldest' | 'popular' | 'trending' | 'controversial';
  filterBy: PostFilter;

  // Search & Discovery
  searchQuery: string;
  searchResults: SearchResult[];
  searchSuggestions: SearchSuggestion[];
  isSearching: boolean;

  // Real-time Features
  onlineUsers: User[];
  typingUsers: Record<string, User[]>;
  liveUpdates: LiveUpdate[];
  connectionStatus: 'connected' | 'connecting' | 'disconnected';

  // Modals & UI
  modals: {
    createPost: boolean;
    editPost: boolean;
    postDetails: boolean;
    userProfile: boolean;
    settings: boolean;
    analytics: boolean;
    moderation: boolean;
  };

  // Editor State
  editor: {
    content: string;
    isRichText: boolean;
    attachments: MediaAttachment[];
    mentions: User[];
    tags: Tag[];
    isUploading: boolean;
    uploadProgress: number;
    isDraft: boolean;
    autosaveEnabled: boolean;
    lastSaved?: Date;
  };

  // Performance & Caching
  loadingStates: Record<string, boolean>;
  errorStates: Record<string, string>;
  cache: {
    posts: Record<string, CommunityPost>;
    users: Record<string, User>;
    lastFetch: Record<string, Date>;
  };

  // Analytics & Insights
  analytics: {
    totalPosts: number;
    totalUsers: number;
    totalEngagement: number;
    growthRate: number;
    topCategories: Category[];
    topTags: Tag[];
    userActivity: UserActivity[];
    contentPerformance: ContentPerformance[];
  };

  // Notifications
  notifications: Notification[];
  unreadCount: number;

  // Preferences
  preferences: UserPreferences;
}

interface PostFilter {
  dateRange?: { start: Date; end: Date };
  authors?: string[];
  categories?: string[];
  tags?: string[];
  minLikes?: number;
  minComments?: number;
  hasMedia?: boolean;
  language?: string;
  verified?: boolean;
}

interface SearchResult {
  type: 'post' | 'user' | 'tag' | 'category';
  id: string;
  title: string;
  description: string;
  relevanceScore: number;
  highlights: string[];
  metadata: Record<string, any>;
}

interface SearchSuggestion {
  type: 'query' | 'user' | 'tag' | 'category';
  text: string;
  count?: number;
  icon?: string;
}

interface LiveUpdate {
  id: string;
  type: 'new_post' | 'new_comment' | 'like' | 'user_joined' | 'user_left';
  data: any;
  timestamp: Date;
  userId?: string;
}

interface UserActivity {
  userId: string;
  user: User;
  postsCount: number;
  commentsCount: number;
  likesReceived: number;
  engagementRate: number;
  lastActive: Date;
}

interface ContentPerformance {
  postId: string;
  post: CommunityPost;
  views: number;
  engagement: number;
  shareRate: number;
  conversionRate: number;
  sentiment: number;
}

interface Notification {
  id: string;
  type: 'like' | 'comment' | 'mention' | 'follow' | 'post' | 'system';
  title: string;
  message: string;
  data: any;
  isRead: boolean;
  createdAt: Date;
  actionUrl?: string;
  icon?: string;
  priority: 'low' | 'medium' | 'high' | 'urgent';
}

interface UserPreferences {
  theme: 'light' | 'dark' | 'auto';
  language: string;
  timezone: string;
  notifications: {
    email: boolean;
    push: boolean;
    inApp: boolean;
    frequency: 'immediate' | 'hourly' | 'daily' | 'weekly';
    types: string[];
  };
  privacy: {
    profileVisibility: 'public' | 'private' | 'followers';
    showOnlineStatus: boolean;
    allowMentions: boolean;
    allowDirectMessages: boolean;
  };
  content: {
    autoplayVideos: boolean;
    showSensitiveContent: boolean;
    defaultPostVisibility: 'public' | 'followers' | 'private';
    enableRichTextEditor: boolean;
  };
  accessibility: {
    reducedMotion: boolean;
    highContrast: boolean;
    fontSize: 'small' | 'medium' | 'large' | 'extra-large';
    screenReader: boolean;
  };
}

// ==================== ACTION TYPES ====================

type CommunityAction =
  | { type: 'SET_POSTS'; payload: CommunityPost[] }
  | { type: 'ADD_POST'; payload: CommunityPost }
  | { type: 'UPDATE_POST'; payload: { id: string; updates: Partial<CommunityPost> } }
  | { type: 'DELETE_POST'; payload: string }
  | { type: 'LIKE_POST'; payload: { postId: string; userId: string } }
  | { type: 'UNLIKE_POST'; payload: { postId: string; userId: string } }
  | { type: 'ADD_COMMENT'; payload: { postId: string; comment: Comment } }
  | { type: 'UPDATE_COMMENT'; payload: { commentId: string; updates: Partial<Comment> } }
  | { type: 'DELETE_COMMENT'; payload: { postId: string; commentId: string } }
  | { type: 'SET_ACTIVE_VIEW'; payload: CommunityState['activeView'] }
  | { type: 'SET_SEARCH_QUERY'; payload: string }
  | { type: 'SET_SEARCH_RESULTS'; payload: SearchResult[] }
  | { type: 'SET_FILTER'; payload: Partial<PostFilter> }
  | { type: 'SET_SORT'; payload: CommunityState['sortBy'] }
  | { type: 'TOGGLE_MODAL'; payload: { modal: keyof CommunityState['modals']; isOpen: boolean } }
  | { type: 'SET_LOADING'; payload: { key: string; isLoading: boolean } }
  | { type: 'SET_ERROR'; payload: { key: string; error: string } }
  | { type: 'UPDATE_EDITOR'; payload: Partial<CommunityState['editor']> }
  | { type: 'ADD_NOTIFICATION'; payload: Notification }
  | { type: 'MARK_NOTIFICATION_READ'; payload: string }
  | { type: 'SET_ONLINE_USERS'; payload: User[] }
  | { type: 'ADD_LIVE_UPDATE'; payload: LiveUpdate }
  | { type: 'UPDATE_CONNECTION_STATUS'; payload: CommunityState['connectionStatus'] }
  | { type: 'UPDATE_PREFERENCES'; payload: Partial<UserPreferences> };

// ==================== ADVANCED STATE REDUCER ====================

const initialState: CommunityState = {
  posts: [],
  comments: {},
  users: {},
  categories: [],
  tags: [],
  activeView: 'feed',
  selectedCategory: 'all',
  selectedTags: [],
  sortBy: 'newest',
  filterBy: {},
  searchQuery: '',
  searchResults: [],
  searchSuggestions: [],
  isSearching: false,
  onlineUsers: [],
  typingUsers: {},
  liveUpdates: [],
  connectionStatus: 'connected',
  modals: {
    createPost: false,
    editPost: false,
    postDetails: false,
    userProfile: false,
    settings: false,
    analytics: false,
    moderation: false,
  },
  editor: {
    content: '',
    isRichText: true,
    attachments: [],
    mentions: [],
    tags: [],
    isUploading: false,
    uploadProgress: 0,
    isDraft: false,
    autosaveEnabled: true,
  },
  loadingStates: {},
  errorStates: {},
  cache: {
    posts: {},
    users: {},
    lastFetch: {},
  },
  analytics: {
    totalPosts: 0,
    totalUsers: 0,
    totalEngagement: 0,
    growthRate: 0,
    topCategories: [],
    topTags: [],
    userActivity: [],
    contentPerformance: [],
  },
  notifications: [],
  unreadCount: 0,
  preferences: {
    theme: 'dark',
    language: 'ar',
    timezone: 'Asia/Damascus',
    notifications: {
      email: true,
      push: true,
      inApp: true,
      frequency: 'immediate',
      types: ['like', 'comment', 'mention', 'follow'],
    },
    privacy: {
      profileVisibility: 'public',
      showOnlineStatus: true,
      allowMentions: true,
      allowDirectMessages: true,
    },
    content: {
      autoplayVideos: false,
      showSensitiveContent: false,
      defaultPostVisibility: 'public',
      enableRichTextEditor: true,
    },
    accessibility: {
      reducedMotion: false,
      highContrast: false,
      fontSize: 'medium',
      screenReader: false,
    },
  },
};

const communityReducer = (state: CommunityState, action: CommunityAction): CommunityState => {
  switch (action.type) {
    case 'SET_POSTS':
      return {
        ...state,
        posts: action.payload,
        cache: {
          ...state.cache,
          posts: action.payload.reduce((acc, post) => ({ ...acc, [post.id]: post }), {}),
          lastFetch: { ...state.cache.lastFetch, posts: new Date() },
        },
      };

    case 'ADD_POST':
      return {
        ...state,
        posts: [action.payload, ...state.posts],
        cache: {
          ...state.cache,
          posts: { ...state.cache.posts, [action.payload.id]: action.payload },
        },
        analytics: {
          ...state.analytics,
          totalPosts: state.analytics.totalPosts + 1,
        },
      };

    case 'UPDATE_POST':
      return {
        ...state,
        posts: state.posts.map(post =>
          post.id === action.payload.id ? { ...post, ...action.payload.updates } : post
        ),
        cache: {
          ...state.cache,
          posts: {
            ...state.cache.posts,
            [action.payload.id]: {
              ...state.cache.posts[action.payload.id],
              ...action.payload.updates,
            },
          },
        },
      };

    case 'DELETE_POST':
      return {
        ...state,
        posts: state.posts.filter(post => post.id !== action.payload),
        cache: {
          ...state.cache,
          posts: Object.fromEntries(
            Object.entries(state.cache.posts).filter(([id]) => id !== action.payload)
          ),
        },
        analytics: {
          ...state.analytics,
          totalPosts: Math.max(0, state.analytics.totalPosts - 1),
        },
      };

    case 'LIKE_POST':
      return {
        ...state,
        posts: state.posts.map(post =>
          post.id === action.payload.postId
            ? {
                ...post,
                engagement: {
                  ...post.engagement,
                  likes: post.engagement.likes + 1,
                  userInteractions: {
                    ...post.engagement.userInteractions,
                    isLiked: true,
                  },
                },
              }
            : post
        ),
      };

    case 'UNLIKE_POST':
      return {
        ...state,
        posts: state.posts.map(post =>
          post.id === action.payload.postId
            ? {
                ...post,
                engagement: {
                  ...post.engagement,
                  likes: Math.max(0, post.engagement.likes - 1),
                  userInteractions: {
                    ...post.engagement.userInteractions,
                    isLiked: false,
                  },
                },
              }
            : post
        ),
      };

    case 'SET_ACTIVE_VIEW':
      return { ...state, activeView: action.payload };

    case 'SET_SEARCH_QUERY':
      return { ...state, searchQuery: action.payload };

    case 'SET_SEARCH_RESULTS':
      return { ...state, searchResults: action.payload, isSearching: false };

    case 'SET_FILTER':
      return { ...state, filterBy: { ...state.filterBy, ...action.payload } };

    case 'SET_SORT':
      return { ...state, sortBy: action.payload };

    case 'TOGGLE_MODAL':
      return {
        ...state,
        modals: { ...state.modals, [action.payload.modal]: action.payload.isOpen },
      };

    case 'SET_LOADING':
      return {
        ...state,
        loadingStates: { ...state.loadingStates, [action.payload.key]: action.payload.isLoading },
      };

    case 'SET_ERROR':
      return {
        ...state,
        errorStates: { ...state.errorStates, [action.payload.key]: action.payload.error },
      };

    case 'UPDATE_EDITOR':
      return {
        ...state,
        editor: { ...state.editor, ...action.payload },
      };

    case 'ADD_NOTIFICATION':
      return {
        ...state,
        notifications: [action.payload, ...state.notifications],
        unreadCount: state.unreadCount + 1,
      };

    case 'MARK_NOTIFICATION_READ':
      return {
        ...state,
        notifications: state.notifications.map(notif =>
          notif.id === action.payload ? { ...notif, isRead: true } : notif
        ),
        unreadCount: Math.max(0, state.unreadCount - 1),
      };

    case 'SET_ONLINE_USERS':
      return { ...state, onlineUsers: action.payload };

    case 'ADD_LIVE_UPDATE':
      return {
        ...state,
        liveUpdates: [action.payload, ...state.liveUpdates.slice(0, 49)], // Keep last 50
      };

    case 'UPDATE_CONNECTION_STATUS':
      return { ...state, connectionStatus: action.payload };

    case 'UPDATE_PREFERENCES':
      return {
        ...state,
        preferences: { ...state.preferences, ...action.payload },
      };

    default:
      return state;
  }
};

const CommunityPage: React.FC = () => {
  const { user } = useAuth();
  const { language } = useLanguage();
  const isRTL = language === 'ar';

  // ==================== ADVANCED STATE MANAGEMENT ====================
  const [state, dispatch] = useReducer(communityReducer, initialState);

  // Performance optimization refs
  const searchTimeoutRef = useRef<NodeJS.Timeout>();
  const virtualListRef = useRef<HTMLDivElement>(null);
  const intersectionObserverRef = useRef<IntersectionObserver>();

  // ==================== ADVANCED HOOKS & MEMOIZATION ====================

  // Memoized selectors for performance
  const filteredPosts = useMemo(() => {
    let filtered = state.posts;

    // Apply category filter
    if (state.selectedCategory !== 'all') {
      filtered = filtered.filter(post => post.category.id === state.selectedCategory);
    }

    // Apply tag filters
    if (state.selectedTags.length > 0) {
      filtered = filtered.filter(post =>
        post.tags.some(tag => state.selectedTags.includes(tag.id))
      );
    }

    // Apply search filter
    if (state.searchQuery.trim()) {
      const query = state.searchQuery.toLowerCase();
      filtered = filtered.filter(post =>
        post.title.toLowerCase().includes(query) ||
        post.content.raw.toLowerCase().includes(query) ||
        post.author.firstName.toLowerCase().includes(query) ||
        post.author.lastName.toLowerCase().includes(query) ||
        post.tags.some(tag => tag.name.toLowerCase().includes(query))
      );
    }

    // Apply additional filters
    if (state.filterBy.dateRange) {
      filtered = filtered.filter(post =>
        post.createdAt >= state.filterBy.dateRange!.start &&
        post.createdAt <= state.filterBy.dateRange!.end
      );
    }

    if (state.filterBy.minLikes) {
      filtered = filtered.filter(post => post.engagement.likes >= state.filterBy.minLikes!);
    }

    if (state.filterBy.hasMedia) {
      filtered = filtered.filter(post => post.media.length > 0);
    }

    if (state.filterBy.verified) {
      filtered = filtered.filter(post => post.author.isVerified);
    }

    // Apply sorting
    switch (state.sortBy) {
      case 'newest':
        return filtered.sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());
      case 'oldest':
        return filtered.sort((a, b) => a.createdAt.getTime() - b.createdAt.getTime());
      case 'popular':
        return filtered.sort((a, b) =>
          (b.engagement.likes + b.engagement.comments + b.engagement.shares) -
          (a.engagement.likes + a.engagement.comments + a.engagement.shares)
        );
      case 'trending':
        return filtered.sort((a, b) => b.engagement.engagementRate - a.engagement.engagementRate);
      case 'controversial':
        return filtered.sort((a, b) =>
          (b.engagement.likes + b.engagement.dislikes) -
          (a.engagement.likes + a.engagement.dislikes)
        );
      default:
        return filtered;
    }
  }, [state.posts, state.selectedCategory, state.selectedTags, state.searchQuery, state.filterBy, state.sortBy]);

  // Debounced search function
  const debouncedSearch = useCallback((query: string) => {
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }

    searchTimeoutRef.current = setTimeout(() => {
      dispatch({ type: 'SET_SEARCH_QUERY', payload: query });

      // Simulate API search
      if (query.trim()) {
        dispatch({ type: 'SET_LOADING', payload: { key: 'search', isLoading: true } });

        setTimeout(() => {
          const results: SearchResult[] = [
            {
              type: 'post',
              id: '1',
              title: 'AI Startup Funding',
              description: 'Discussion about AI startup funding trends',
              relevanceScore: 0.95,
              highlights: ['AI', 'startup', 'funding'],
              metadata: { category: 'startup', likes: 247 }
            },
            {
              type: 'user',
              id: 'user1',
              title: 'Dr. Sarah Ahmed',
              description: 'Tech Entrepreneur specializing in AI',
              relevanceScore: 0.87,
              highlights: ['AI', 'entrepreneur'],
              metadata: { verified: true, followers: 1250 }
            }
          ];

          dispatch({ type: 'SET_SEARCH_RESULTS', payload: results });
          dispatch({ type: 'SET_LOADING', payload: { key: 'search', isLoading: false } });
        }, 800);
      } else {
        dispatch({ type: 'SET_SEARCH_RESULTS', payload: [] });
      }
    }, 300);
  }, []);

  // Real-time connection simulation
  useEffect(() => {
    const interval = setInterval(() => {
      // Simulate online users fluctuation
      const onlineCount = 120 + Math.floor(Math.random() * 20);
      const mockOnlineUsers: User[] = Array.from({ length: Math.min(onlineCount, 10) }, (_, i) => ({
        id: `online-${i}`,
        username: `user${i}`,
        firstName: `User`,
        lastName: `${i}`,
        email: `user${i}@example.com`,
        role: { id: 'user', name: 'User', level: 1, permissions: [], color: '#6B7280', icon: 'user' },
        isVerified: Math.random() > 0.7,
        isOnline: true,
        lastSeen: new Date(),
        joinedAt: new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000),
        reputation: Math.floor(Math.random() * 5000),
        followersCount: Math.floor(Math.random() * 1000),
        followingCount: Math.floor(Math.random() * 500),
        postsCount: Math.floor(Math.random() * 100),
        skills: [],
        interests: [],
        achievements: []
      }));

      dispatch({ type: 'SET_ONLINE_USERS', payload: mockOnlineUsers });

      // Simulate live updates
      if (Math.random() > 0.7) {
        const liveUpdate: LiveUpdate = {
          id: `update-${Date.now()}`,
          type: Math.random() > 0.5 ? 'new_post' : 'like',
          data: { message: 'New activity in the community' },
          timestamp: new Date(),
          userId: `user-${Math.floor(Math.random() * 100)}`
        };

        dispatch({ type: 'ADD_LIVE_UPDATE', payload: liveUpdate });
      }
    }, 5000);

    return () => clearInterval(interval);
  }, []);

  // Initialize with enterprise-level mock data
  useEffect(() => {
    const mockCategories: Category[] = [
      {
        id: 'startup',
        name: isRTL ? 'شركات ناشئة' : 'Startups',
        description: isRTL ? 'مناقشات حول الشركات الناشئة' : 'Discussions about startups',
        icon: 'rocket',
        color: '#8B5CF6',
        children: [],
        postCount: 234,
        isActive: true,
        moderators: [],
        rules: [],
        tags: []
      },
      {
        id: 'ai',
        name: isRTL ? 'الذكاء الاصطناعي' : 'Artificial Intelligence',
        description: isRTL ? 'تقنيات الذكاء الاصطناعي' : 'AI technologies and discussions',
        icon: 'brain',
        color: '#06B6D4',
        children: [],
        postCount: 189,
        isActive: true,
        moderators: [],
        rules: [],
        tags: []
      },
      {
        id: 'funding',
        name: isRTL ? 'التمويل' : 'Funding',
        description: isRTL ? 'فرص التمويل والاستثمار' : 'Funding and investment opportunities',
        icon: 'dollar-sign',
        color: '#10B981',
        children: [],
        postCount: 156,
        isActive: true,
        moderators: [],
        rules: [],
        tags: []
      }
    ];

    const mockTags: Tag[] = [
      { id: 'startup-funding', name: '#StartupFunding', color: '#8B5CF6', usageCount: 234, trending: true, trendingScore: 12, createdAt: new Date() },
      { id: 'ai-tech', name: '#AI', color: '#06B6D4', usageCount: 189, trending: true, trendingScore: 8, createdAt: new Date() },
      { id: 'product-launch', name: '#ProductLaunch', color: '#F59E0B', usageCount: 156, trending: true, trendingScore: 15, createdAt: new Date() }
    ];

    // Create mock users
    const mockUsers: Record<string, User> = {
      'user1': {
        id: 'user1',
        username: 'sarah_ahmed',
        firstName: 'Dr. Sarah',
        lastName: 'Ahmed',
        email: '<EMAIL>',
        avatar: '/api/placeholder/40/40',
        role: {
          id: 'entrepreneur',
          name: 'Tech Entrepreneur',
          level: 5,
          permissions: [],
          color: '#8B5CF6',
          icon: 'rocket'
        },
        badge: {
          id: 'verified',
          name: 'Verified',
          description: 'Verified entrepreneur',
          icon: 'check-circle',
          color: '#10B981',
          rarity: 'rare'
        },
        isVerified: true,
        isOnline: true,
        lastSeen: new Date(),
        joinedAt: new Date('2022-01-15'),
        reputation: 2840,
        followersCount: 1250,
        followingCount: 340,
        postsCount: 89,
        bio: 'Building the future of AI-powered entrepreneurship',
        location: 'Dubai, UAE',
        website: 'https://sarahtech.ai',
        skills: ['AI', 'Machine Learning', 'Entrepreneurship', 'Product Management'],
        interests: ['Technology', 'Innovation', 'Startups'],
        achievements: []
      },
      'current_user': {
        id: user?.id?.toString() || 'current_user',
        username: 'current_user',
        firstName: user?.first_name || 'Current',
        lastName: user?.last_name || 'User',
        email: user?.email || '<EMAIL>',
        role: {
          id: 'user',
          name: 'Community Member',
          level: 2,
          permissions: [],
          color: '#6B7280',
          icon: 'user'
        },
        isVerified: false,
        isOnline: true,
        lastSeen: new Date(),
        joinedAt: new Date('2023-06-01'),
        reputation: 156,
        followersCount: 45,
        followingCount: 123,
        postsCount: 12,
        skills: [],
        interests: [],
        achievements: []
      }
    };

    // Enhanced posts with enterprise-level data structure
    const mockPosts: CommunityPost[] = [
      {
        id: '1',
        author: mockUsers['user1'],
        title: '🚀 Just secured $2M Series A funding for our AI startup!',
        content: {
          raw: 'After 18 months of hard work, we\'ve successfully raised our Series A round! Our AI-powered platform for connecting Syrian entrepreneurs with global mentors has reached 10K+ users. Looking to expand our team - DM me if you\'re interested in joining our mission to democratize entrepreneurship in the MENA region. #StartupLife #AI #Funding',
          formatted: '<p>After 18 months of hard work, we\'ve successfully raised our Series A round! Our AI-powered platform for connecting Syrian entrepreneurs with global mentors has reached <strong>10K+ users</strong>.</p><p>Looking to expand our team - DM me if you\'re interested in joining our mission to democratize entrepreneurship in the MENA region.</p><p><a href="#StartupLife">#StartupLife</a> <a href="#AI">#AI</a> <a href="#Funding">#Funding</a></p>',
          excerpt: 'After 18 months of hard work, we\'ve successfully raised our Series A round!',
          wordCount: 67,
          readingTime: 1,
          language: 'en',
          sentiment: 'positive'
        },
        category: mockCategories[0],
        tags: [mockTags[0], mockTags[1]],
        createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
        visibility: 'public',
        status: 'published',
        engagement: {
          likes: 247,
          dislikes: 3,
          comments: 38,
          shares: 15,
          saves: 67,
          views: 1560,
          uniqueViews: 1340,
          clickThroughRate: 0.12,
          engagementRate: 0.18,
          userInteractions: {
            isLiked: false,
            isDisliked: false,
            isSaved: false,
            isShared: false,
            hasCommented: false,
            hasViewed: true
          }
        },
        media: [
          {
            id: 'media1',
            type: 'image',
            url: '/api/placeholder/600/400',
            thumbnailUrl: '/api/placeholder/300/200',
            filename: 'funding-announcement.jpg',
            size: 245760,
            mimeType: 'image/jpeg',
            dimensions: { width: 600, height: 400 },
            metadata: { alt: 'Funding announcement celebration' },
            uploadedAt: new Date(),
            isProcessing: false
          }
        ],
        mentions: [],
        isEdited: false,
        editHistory: [],
        moderationFlags: [],
        analytics: {
          impressions: 2340,
          reach: 1890,
          engagement: 367,
          clickThroughRate: 0.12,
          shareRate: 0.009,
          saveRate: 0.043,
          commentRate: 0.024,
          demographics: {
            ageGroups: { '25-34': 45, '35-44': 30, '18-24': 15, '45+': 10 },
            locations: { 'UAE': 35, 'Saudi Arabia': 25, 'Egypt': 20, 'Other': 20 },
            devices: { 'Mobile': 65, 'Desktop': 30, 'Tablet': 5 },
            referrers: { 'Direct': 40, 'Social': 35, 'Search': 25 }
          },
          timeSeriesData: []
        },
        permissions: {
          canEdit: false,
          canDelete: false,
          canPin: false,
          canArchive: false,
          canModerate: false,
          canViewAnalytics: false,
          canExport: false
        }
      },
      {
        id: '2',
        author: mockUsers['current_user'],
        title: '💡 5 Essential Tips for Successful Investor Pitching',
        content: {
          raw: 'Just finished my 50th investor pitch and wanted to share what I\'ve learned:\n\n1. Start with the problem, not your solution\n2. Show traction with real numbers\n3. Know your market size inside out\n4. Have a clear ask and use of funds\n5. Practice until you can pitch in your sleep\n\nWhat would you add to this list? #InvestorPitch #StartupTips #Entrepreneurship',
          formatted: '<p>Just finished my 50th investor pitch and wanted to share what I\'ve learned:</p><ol><li>Start with the problem, not your solution</li><li>Show traction with real numbers</li><li>Know your market size inside out</li><li>Have a clear ask and use of funds</li><li>Practice until you can pitch in your sleep</li></ol><p>What would you add to this list?</p><p><a href="#InvestorPitch">#InvestorPitch</a> <a href="#StartupTips">#StartupTips</a> <a href="#Entrepreneurship">#Entrepreneurship</a></p>',
          excerpt: 'Just finished my 50th investor pitch and wanted to share what I\'ve learned',
          wordCount: 89,
          readingTime: 1,
          language: 'en',
          sentiment: 'positive'
        },
        category: mockCategories[0],
        tags: [mockTags[0]],
        createdAt: new Date(Date.now() - 5 * 60 * 60 * 1000), // 5 hours ago
        visibility: 'public',
        status: 'published',
        engagement: {
          likes: 189,
          dislikes: 2,
          comments: 42,
          shares: 23,
          saves: 34,
          views: 892,
          uniqueViews: 756,
          clickThroughRate: 0.15,
          engagementRate: 0.29,
          userInteractions: {
            isLiked: true,
            isDisliked: false,
            isSaved: true,
            isShared: false,
            hasCommented: true,
            hasViewed: true
          }
        },
        media: [],
        mentions: [],
        isEdited: false,
        editHistory: [],
        moderationFlags: [],
        analytics: {
          impressions: 1240,
          reach: 980,
          engagement: 290,
          clickThroughRate: 0.15,
          shareRate: 0.026,
          saveRate: 0.038,
          commentRate: 0.047,
          demographics: {
            ageGroups: { '25-34': 50, '35-44': 25, '18-24': 15, '45+': 10 },
            locations: { 'UAE': 30, 'Saudi Arabia': 25, 'Egypt': 25, 'Other': 20 },
            devices: { 'Mobile': 70, 'Desktop': 25, 'Tablet': 5 },
            referrers: { 'Direct': 45, 'Social': 30, 'Search': 25 }
          },
          timeSeriesData: []
        },
        permissions: {
          canEdit: true,
          canDelete: true,
          canPin: false,
          canArchive: true,
          canModerate: false,
          canViewAnalytics: true,
          canExport: true
        }
      }
    ];

    // Initialize state with mock data
    dispatch({ type: 'SET_POSTS', payload: mockPosts });

    // Set up analytics
    const analytics = {
      totalPosts: mockPosts.length,
      totalUsers: Object.keys(mockUsers).length,
      totalEngagement: mockPosts.reduce((sum, post) => sum + post.engagement.likes + post.engagement.comments, 0),
      growthRate: 12.5,
      topCategories: mockCategories,
      topTags: mockTags,
      userActivity: [],
      contentPerformance: []
    };

    // Simulate initial notifications
    const initialNotifications: Notification[] = [
      {
        id: 'notif1',
        type: 'like',
        title: 'New Like',
        message: 'Dr. Sarah Ahmed liked your post',
        data: { postId: '2', userId: 'user1' },
        isRead: false,
        createdAt: new Date(Date.now() - 30 * 60 * 1000),
        priority: 'medium',
        icon: 'heart'
      },
      {
        id: 'notif2',
        type: 'comment',
        title: 'New Comment',
        message: 'Someone commented on your post about investor pitching',
        data: { postId: '2', commentId: 'comment1' },
        isRead: false,
        createdAt: new Date(Date.now() - 60 * 60 * 1000),
        priority: 'high',
        icon: 'message-circle'
      },
      {
        id: 'notif3',
        type: 'mention',
        title: 'You were mentioned',
        message: 'Omar Al-Rashid mentioned you in a discussion',
        data: { postId: '3', userId: 'user3' },
        isRead: true,
        createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000),
        priority: 'medium',
        icon: 'at-sign'
      }
    ];

    initialNotifications.forEach(notification => {
      dispatch({ type: 'ADD_NOTIFICATION', payload: notification });
    });

  }, [user, isRTL]);

  // ==================== ADVANCED ACTION HANDLERS ====================

  // Optimized post interaction handlers
  const handleLikePost = useCallback((postId: string) => {
    const post = state.posts.find(p => p.id === postId);
    if (!post) return;

    const isCurrentlyLiked = post.engagement.userInteractions.isLiked;

    if (isCurrentlyLiked) {
      dispatch({ type: 'UNLIKE_POST', payload: { postId, userId: user?.id?.toString() || 'current_user' } });
    } else {
      dispatch({ type: 'LIKE_POST', payload: { postId, userId: user?.id?.toString() || 'current_user' } });

      // Add notification for post author (if not self)
      if (post.author.id !== (user?.id?.toString() || 'current_user')) {
        const notification: Notification = {
          id: `like-${Date.now()}`,
          type: 'like',
          title: isRTL ? 'إعجاب جديد' : 'New Like',
          message: isRTL ? `أعجب ${user?.first_name || 'شخص ما'} بمنشورك` : `${user?.first_name || 'Someone'} liked your post`,
          data: { postId, userId: user?.id?.toString() || 'current_user' },
          isRead: false,
          createdAt: new Date(),
          priority: 'low',
          icon: 'heart'
        };
        dispatch({ type: 'ADD_NOTIFICATION', payload: notification });
      }
    }

    // Simulate real-time update
    const liveUpdate: LiveUpdate = {
      id: `like-${Date.now()}`,
      type: 'like',
      data: { postId, action: isCurrentlyLiked ? 'unlike' : 'like' },
      timestamp: new Date(),
      userId: user?.id?.toString() || 'current_user'
    };
    dispatch({ type: 'ADD_LIVE_UPDATE', payload: liveUpdate });
  }, [state.posts, user, isRTL]);

  const handleSavePost = useCallback((postId: string) => {
    const post = state.posts.find(p => p.id === postId);
    if (!post) return;

    const isSaved = post.engagement.userInteractions.isSaved;

    dispatch({
      type: 'UPDATE_POST',
      payload: {
        id: postId,
        updates: {
          engagement: {
            ...post.engagement,
            saves: isSaved ? post.engagement.saves - 1 : post.engagement.saves + 1,
            userInteractions: {
              ...post.engagement.userInteractions,
              isSaved: !isSaved
            }
          }
        }
      }
    });
  }, [state.posts]);

  const handleSharePost = useCallback((postId: string) => {
    const post = state.posts.find(p => p.id === postId);
    if (!post) return;

    // Simulate sharing functionality
    if (navigator.share) {
      navigator.share({
        title: post.title,
        text: post.content.excerpt,
        url: `${window.location.origin}/post/${postId}`
      });
    } else {
      // Fallback: copy to clipboard
      navigator.clipboard.writeText(`${window.location.origin}/post/${postId}`);

      const notification: Notification = {
        id: `share-${Date.now()}`,
        type: 'system',
        title: isRTL ? 'تم النسخ' : 'Copied',
        message: isRTL ? 'تم نسخ رابط المنشور' : 'Post link copied to clipboard',
        data: { postId },
        isRead: false,
        createdAt: new Date(),
        priority: 'low',
        icon: 'link'
      };
      dispatch({ type: 'ADD_NOTIFICATION', payload: notification });
    }

    // Update share count
    dispatch({
      type: 'UPDATE_POST',
      payload: {
        id: postId,
        updates: {
          engagement: {
            ...post.engagement,
            shares: post.engagement.shares + 1,
            userInteractions: {
              ...post.engagement.userInteractions,
              isShared: true
            }
          }
        }
      }
    });
  }, [state.posts, isRTL]);

  const handleDeletePost = useCallback((postId: string) => {
    if (window.confirm(isRTL ? 'هل أنت متأكد من حذف هذا المنشور؟' : 'Are you sure you want to delete this post?')) {
      dispatch({ type: 'DELETE_POST', payload: postId });

      const notification: Notification = {
        id: `delete-${Date.now()}`,
        type: 'system',
        title: isRTL ? 'تم الحذف' : 'Deleted',
        message: isRTL ? 'تم حذف المنشور بنجاح' : 'Post deleted successfully',
        data: { postId },
        isRead: false,
        createdAt: new Date(),
        priority: 'medium',
        icon: 'trash-2'
      };
      dispatch({ type: 'ADD_NOTIFICATION', payload: notification });
    }
  }, [isRTL]);

  const handleCreatePost = useCallback((postData: {
    title: string;
    content: string;
    category: string;
    tags: string[];
    media: MediaAttachment[];
    visibility: 'public' | 'private' | 'followers';
  }) => {
    const newPost: CommunityPost = {
      id: `post-${Date.now()}`,
      author: state.users[user?.id?.toString() || 'current_user'] || {
        id: user?.id?.toString() || 'current_user',
        username: 'current_user',
        firstName: user?.first_name || 'Current',
        lastName: user?.last_name || 'User',
        email: user?.email || '<EMAIL>',
        role: {
          id: 'user',
          name: 'Community Member',
          level: 1,
          permissions: [],
          color: '#6B7280',
          icon: 'user'
        },
        isVerified: false,
        isOnline: true,
        lastSeen: new Date(),
        joinedAt: new Date(),
        reputation: 0,
        followersCount: 0,
        followingCount: 0,
        postsCount: 0,
        skills: [],
        interests: [],
        achievements: []
      },
      title: postData.title,
      content: {
        raw: postData.content,
        formatted: postData.content.replace(/\n/g, '<br>'),
        excerpt: postData.content.substring(0, 150) + (postData.content.length > 150 ? '...' : ''),
        wordCount: postData.content.split(' ').length,
        readingTime: Math.ceil(postData.content.split(' ').length / 200),
        language: isRTL ? 'ar' : 'en'
      },
      category: state.categories.find(c => c.id === postData.category) || state.categories[0],
      tags: state.tags.filter(t => postData.tags.includes(t.id)),
      createdAt: new Date(),
      visibility: postData.visibility,
      status: 'published',
      engagement: {
        likes: 0,
        dislikes: 0,
        comments: 0,
        shares: 0,
        saves: 0,
        views: 1,
        uniqueViews: 1,
        clickThroughRate: 0,
        engagementRate: 0,
        userInteractions: {
          isLiked: false,
          isDisliked: false,
          isSaved: false,
          isShared: false,
          hasCommented: false,
          hasViewed: true
        }
      },
      media: postData.media,
      mentions: [],
      isEdited: false,
      editHistory: [],
      moderationFlags: [],
      analytics: {
        impressions: 1,
        reach: 1,
        engagement: 0,
        clickThroughRate: 0,
        shareRate: 0,
        saveRate: 0,
        commentRate: 0,
        demographics: {
          ageGroups: {},
          locations: {},
          devices: {},
          referrers: {}
        },
        timeSeriesData: []
      },
      permissions: {
        canEdit: true,
        canDelete: true,
        canPin: false,
        canArchive: true,
        canModerate: false,
        canViewAnalytics: true,
        canExport: true
      }
    };

    dispatch({ type: 'ADD_POST', payload: newPost });
    dispatch({ type: 'TOGGLE_MODAL', payload: { modal: 'createPost', isOpen: false } });

    const notification: Notification = {
      id: `create-${Date.now()}`,
      type: 'system',
      title: isRTL ? 'تم النشر' : 'Published',
      message: isRTL ? 'تم نشر منشورك بنجاح' : 'Your post has been published successfully',
      data: { postId: newPost.id },
      isRead: false,
      createdAt: new Date(),
      priority: 'medium',
      icon: 'check-circle'
    };
    dispatch({ type: 'ADD_NOTIFICATION', payload: notification });
  }, [state.users, state.categories, state.tags, user, isRTL]);

  // Advanced search handler with debouncing
  const handleSearch = useCallback((query: string) => {
    debouncedSearch(query);
  }, [debouncedSearch]);

  // Filter and sort handlers
  const handleFilterChange = useCallback((filter: Partial<PostFilter>) => {
    dispatch({ type: 'SET_FILTER', payload: filter });
  }, []);

  const handleSortChange = useCallback((sortBy: CommunityState['sortBy']) => {
    dispatch({ type: 'SET_SORT', payload: sortBy });
  }, []);

  const handleViewChange = useCallback((view: CommunityState['activeView']) => {
    dispatch({ type: 'SET_ACTIVE_VIEW', payload: view });
  }, []);

  // Modal handlers
  const handleModalToggle = useCallback((modal: keyof CommunityState['modals'], isOpen: boolean) => {
    dispatch({ type: 'TOGGLE_MODAL', payload: { modal, isOpen } });
  }, []);

  // Notification handlers
  const handleNotificationRead = useCallback((notificationId: string) => {
    dispatch({ type: 'MARK_NOTIFICATION_READ', payload: notificationId });
  }, []);

  const handleNotificationClear = useCallback(() => {
    // Clear all notifications
    state.notifications.forEach(notification => {
      if (!notification.isRead) {
        dispatch({ type: 'MARK_NOTIFICATION_READ', payload: notification.id });
      }
    });
  }, [state.notifications]);

  // ==================== ENTERPRISE-LEVEL UI COMPONENT ====================

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-violet-900 relative overflow-hidden">
      {/* Animated Background Elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-purple-500/10 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-blue-500/10 rounded-full blur-3xl animate-pulse delay-1000"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-violet-500/5 rounded-full blur-3xl animate-pulse delay-2000"></div>
      </div>

      {/* Connection Status Indicator */}
      <div className={`fixed top-4 right-4 z-50 px-3 py-1 rounded-full text-xs font-medium transition-all duration-300 ${
        state.connectionStatus === 'connected'
          ? 'bg-green-500/20 text-green-400 border border-green-500/30'
          : state.connectionStatus === 'connecting'
          ? 'bg-yellow-500/20 text-yellow-400 border border-yellow-500/30'
          : 'bg-red-500/20 text-red-400 border border-red-500/30'
      }`}>
        <div className="flex items-center gap-2">
          {state.connectionStatus === 'connected' ? (
            <Wifi className="w-3 h-3" />
          ) : state.connectionStatus === 'connecting' ? (
            <RefreshCw className="w-3 h-3 animate-spin" />
          ) : (
            <WifiOff className="w-3 h-3" />
          )}
          {isRTL ? (
            state.connectionStatus === 'connected' ? 'متصل' :
            state.connectionStatus === 'connecting' ? 'جاري الاتصال' : 'غير متصل'
          ) : (
            state.connectionStatus === 'connected' ? 'Connected' :
            state.connectionStatus === 'connecting' ? 'Connecting' : 'Disconnected'
          )}
        </div>
      </div>

      {/* Advanced Header with Navigation */}
      <div className="bg-black/20 backdrop-blur-xl border-b border-white/10 sticky top-0 z-40 shadow-2xl">
        <div className="max-w-7xl mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            {/* Logo and Title */}
            <div className="flex items-center space-x-4">
              <div className="relative">
                <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-blue-500 rounded-xl flex items-center justify-center shadow-lg">
                  <Users className="w-7 h-7 text-white" />
                </div>
                <div className="absolute -top-1 -right-1 w-4 h-4 bg-green-500 rounded-full border-2 border-gray-900 animate-pulse"></div>
              </div>
              <div>
                <h1 className="text-2xl font-bold bg-gradient-to-r from-purple-400 to-blue-400 bg-clip-text text-transparent">
                  {isRTL ? 'مجتمع ياسمين' : 'Yasmeen Community'}
                </h1>
                <p className="text-gray-400 text-sm flex items-center gap-2">
                  <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                  {isRTL ? `${state.onlineUsers.length} متصل الآن` : `${state.onlineUsers.length} online now`}
                </p>
              </div>
            </div>

            {/* Advanced Search Bar */}
            <div className="hidden md:flex flex-1 max-w-md mx-8">
              <div className="relative w-full group">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5 group-focus-within:text-purple-400 transition-colors duration-200" />
                <input
                  type="text"
                  value={state.searchQuery}
                  onChange={(e) => handleSearch(e.target.value)}
                  placeholder={isRTL ? 'البحث في المجتمع...' : 'Search community...'}
                  className="w-full bg-white/10 border border-white/20 rounded-xl pl-10 pr-4 py-3 text-white placeholder-gray-400 focus:outline-none focus:border-purple-400 focus:bg-white/15 focus:shadow-lg focus:shadow-purple-500/25 transition-all duration-300"
                />
                {state.isSearching && (
                  <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                    <RefreshCw className="w-4 h-4 text-purple-400 animate-spin" />
                  </div>
                )}

                {/* Search Suggestions Dropdown */}
                {state.searchQuery && state.searchResults.length > 0 && (
                  <div className="absolute top-full left-0 right-0 mt-2 bg-black/90 backdrop-blur-sm rounded-xl border border-white/20 shadow-2xl z-50 max-h-96 overflow-y-auto">
                    {state.searchResults.map((result, index) => (
                      <div key={result.id} className="p-3 hover:bg-white/10 cursor-pointer transition-all duration-200 border-b border-white/5 last:border-b-0">
                        <div className="flex items-center gap-3">
                          <div className={`w-8 h-8 rounded-lg flex items-center justify-center ${
                            result.type === 'post' ? 'bg-purple-500/20 text-purple-400' :
                            result.type === 'user' ? 'bg-blue-500/20 text-blue-400' :
                            result.type === 'tag' ? 'bg-green-500/20 text-green-400' :
                            'bg-gray-500/20 text-gray-400'
                          }`}>
                            {result.type === 'post' ? <FileText className="w-4 h-4" /> :
                             result.type === 'user' ? <Users className="w-4 h-4" /> :
                             result.type === 'tag' ? <Hash className="w-4 h-4" /> :
                             <Search className="w-4 h-4" />}
                          </div>
                          <div className="flex-1">
                            <h4 className="text-white font-medium text-sm">{result.title}</h4>
                            <p className="text-gray-400 text-xs">{result.description}</p>
                          </div>
                          <div className="text-xs text-gray-500">
                            {Math.round(result.relevanceScore * 100)}%
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex items-center space-x-4">
              {/* Notifications */}
              <div className="relative group">
                <button
                  onClick={() => handleModalToggle('settings', true)}
                  className="relative p-3 text-gray-400 hover:text-white hover:bg-white/10 rounded-xl transition-all duration-200"
                >
                  <Bell className="w-6 h-6" />
                  {state.unreadCount > 0 && (
                    <span className="absolute -top-1 -right-1 w-5 h-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center animate-pulse">
                      {state.unreadCount > 99 ? '99+' : state.unreadCount}
                    </span>
                  )}
                </button>

                {/* Notifications Dropdown */}
                <div className="absolute right-0 top-full mt-2 w-80 bg-black/90 backdrop-blur-sm rounded-xl border border-white/20 shadow-2xl opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 z-50">
                  <div className="p-4 border-b border-white/10">
                    <div className="flex items-center justify-between">
                      <h3 className="text-white font-semibold">{isRTL ? 'الإشعارات' : 'Notifications'}</h3>
                      {state.unreadCount > 0 && (
                        <button
                          onClick={handleNotificationClear}
                          className="text-purple-400 hover:text-purple-300 text-sm"
                        >
                          {isRTL ? 'تمييز الكل كمقروء' : 'Mark all read'}
                        </button>
                      )}
                    </div>
                  </div>
                  <div className="max-h-64 overflow-y-auto">
                    {state.notifications.slice(0, 5).map((notification) => (
                      <div
                        key={notification.id}
                        onClick={() => handleNotificationRead(notification.id)}
                        className={`p-3 hover:bg-white/5 cursor-pointer transition-all duration-200 border-b border-white/5 last:border-b-0 ${
                          !notification.isRead ? 'bg-purple-500/5' : ''
                        }`}
                      >
                        <div className="flex items-start gap-3">
                          <div className={`w-8 h-8 rounded-lg flex items-center justify-center ${
                            notification.type === 'like' ? 'bg-red-500/20 text-red-400' :
                            notification.type === 'comment' ? 'bg-blue-500/20 text-blue-400' :
                            notification.type === 'mention' ? 'bg-green-500/20 text-green-400' :
                            'bg-gray-500/20 text-gray-400'
                          }`}>
                            {notification.type === 'like' ? <Heart className="w-4 h-4" /> :
                             notification.type === 'comment' ? <MessageCircle className="w-4 h-4" /> :
                             notification.type === 'mention' ? <AtSign className="w-4 h-4" /> :
                             <Bell className="w-4 h-4" />}
                          </div>
                          <div className="flex-1">
                            <h4 className="text-white font-medium text-sm">{notification.title}</h4>
                            <p className="text-gray-400 text-xs">{notification.message}</p>
                            <p className="text-gray-500 text-xs mt-1">
                              {new Date(notification.createdAt).toLocaleString()}
                            </p>
                          </div>
                          {!notification.isRead && (
                            <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>

              {/* Settings */}
              <button
                onClick={() => handleModalToggle('settings', true)}
                className="p-3 text-gray-400 hover:text-white hover:bg-white/10 rounded-xl transition-all duration-200"
              >
                <Settings className="w-6 h-6" />
              </button>

              {/* Create Post Button */}
              <button
                onClick={() => handleModalToggle('createPost', true)}
                className="bg-gradient-to-r from-purple-600 to-blue-600 text-white px-6 py-3 rounded-xl flex items-center gap-2 hover:shadow-lg hover:shadow-purple-500/25 transition-all duration-300 font-medium transform hover:scale-105"
              >
                <Plus className="w-5 h-5" />
                {isRTL ? 'منشور جديد' : 'New Post'}
              </button>
            </div>
          </div>

          {/* Advanced Navigation Tabs */}
          <div className="flex items-center justify-between mt-6">
            <div className="flex items-center space-x-1 bg-white/5 rounded-xl p-1 backdrop-blur-sm">
              {[
                { id: 'feed', label: isRTL ? 'الخلاصة' : 'Feed', icon: <Globe className="w-4 h-4" />, count: filteredPosts.length },
                { id: 'trending', label: isRTL ? 'الرائج' : 'Trending', icon: <TrendingUp className="w-4 h-4" />, count: 23 },
                { id: 'following', label: isRTL ? 'المتابعون' : 'Following', icon: <Users className="w-4 h-4" />, count: 12 },
                { id: 'saved', label: isRTL ? 'المحفوظات' : 'Saved', icon: <Bookmark className="w-4 h-4" />, count: 8 }
              ].map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => handleViewChange(tab.id as any)}
                  className={`flex items-center gap-2 px-4 py-2 rounded-lg transition-all duration-300 relative ${
                    state.activeView === tab.id
                      ? 'bg-gradient-to-r from-purple-600 to-blue-600 text-white shadow-lg transform scale-105'
                      : 'text-gray-400 hover:text-white hover:bg-white/10'
                  }`}
                >
                  {tab.icon}
                  <span className="font-medium">{tab.label}</span>
                  <span className={`text-xs px-2 py-0.5 rounded-full ${
                    state.activeView === tab.id
                      ? 'bg-white/20 text-white'
                      : 'bg-gray-500/20 text-gray-400'
                  }`}>
                    {tab.count}
                  </span>
                </button>
              ))}
            </div>

            {/* Advanced Filters */}
            <div className="flex items-center space-x-3">
              {/* Sort Options */}
              <div className="flex items-center gap-2">
                <button
                  onClick={() => handleSortChange(state.sortBy === 'newest' ? 'oldest' : 'newest')}
                  className="p-2 text-gray-400 hover:text-white hover:bg-white/10 rounded-lg transition-all duration-200"
                  title={isRTL ? 'ترتيب' : 'Sort'}
                >
                  {state.sortBy === 'newest' ? <SortDesc className="w-5 h-5" /> : <SortAsc className="w-5 h-5" />}
                </button>
              </div>

              {/* Category Filter */}
              <div className="flex items-center space-x-2">
                <Filter className="w-5 h-5 text-gray-400" />
                <select
                  value={state.selectedCategory}
                  onChange={(e) => handleFilterChange({ categories: e.target.value === 'all' ? [] : [e.target.value] })}
                  className="bg-white/10 border border-white/20 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-purple-400 focus:shadow-lg focus:shadow-purple-500/25 transition-all duration-300"
                >
                  <option value="all" className="bg-gray-800">{isRTL ? 'جميع الفئات' : 'All Categories'}</option>
                  {state.categories.map((category) => (
                    <option key={category.id} value={category.id} className="bg-gray-800">
                      {category.name}
                    </option>
                  ))}
                </select>
              </div>

              {/* Advanced Filter Button */}
              <button
                onClick={() => handleModalToggle('settings', true)}
                className="p-2 text-gray-400 hover:text-white hover:bg-white/10 rounded-lg transition-all duration-200"
                title={isRTL ? 'فلاتر متقدمة' : 'Advanced Filters'}
              >
                <Settings className="w-5 h-5" />
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content Area */}
      <div className="max-w-7xl mx-auto px-6 py-8 relative">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">

          {/* Left Sidebar - Community Stats & Quick Actions */}
          <div className="lg:col-span-1 space-y-6">
            {/* Live Stats */}
            <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 border border-white/10 shadow-xl">
              <h3 className="text-lg font-bold text-white mb-4 flex items-center gap-2">
                <Fire className="w-5 h-5 text-orange-400" />
                {isRTL ? 'إحصائيات مباشرة' : 'Live Stats'}
              </h3>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                    <span className="text-gray-300 text-sm">{isRTL ? 'متصل الآن' : 'Online Now'}</span>
                  </div>
                  <span className="text-white font-bold">{state.onlineUsers.length}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-300 text-sm">{isRTL ? 'إجمالي الأعضاء' : 'Total Members'}</span>
                  <span className="text-white font-bold">{state.analytics.totalUsers.toLocaleString()}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-300 text-sm">{isRTL ? 'منشورات اليوم' : 'Posts Today'}</span>
                  <span className="text-white font-bold">{state.analytics.totalPosts}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-300 text-sm">{isRTL ? 'تفاعلات اليوم' : 'Interactions'}</span>
                  <span className="text-white font-bold">{state.analytics.totalEngagement.toLocaleString()}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-300 text-sm">{isRTL ? 'معدل النمو' : 'Growth Rate'}</span>
                  <span className="text-green-400 font-bold">+{state.analytics.growthRate}%</span>
                </div>
              </div>
            </div>

            {/* Trending Topics */}
            <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 border border-white/10">
              <h3 className="text-lg font-bold text-white mb-4 flex items-center gap-2">
                <Hash className="w-5 h-5 text-blue-400" />
                {isRTL ? 'المواضيع الرائجة' : 'Trending Topics'}
              </h3>
              <div className="space-y-3">
                {[
                  { tag: '#StartupFunding', posts: 234, trend: '+12%' },
                  { tag: '#AI', posts: 189, trend: '+8%' },
                  { tag: '#ProductLaunch', posts: 156, trend: '+15%' },
                  { tag: '#Networking', posts: 134, trend: '+5%' },
                  { tag: '#InvestorPitch', posts: 98, trend: '+22%' }
                ].map((topic, index) => (
                  <div key={index} className="flex items-center justify-between p-2 rounded-lg hover:bg-white/5 cursor-pointer transition-all duration-200">
                    <div>
                      <p className="text-purple-400 font-medium text-sm">{topic.tag}</p>
                      <p className="text-gray-400 text-xs">{topic.posts} {isRTL ? 'منشور' : 'posts'}</p>
                    </div>
                    <span className="text-green-400 text-xs font-medium">{topic.trend}</span>
                  </div>
                ))}
              </div>
            </div>

            {/* Top Contributors */}
            <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 border border-white/10">
              <h3 className="text-lg font-bold text-white mb-4 flex items-center gap-2">
                <Crown className="w-5 h-5 text-yellow-400" />
                {isRTL ? 'أفضل المساهمين' : 'Top Contributors'}
              </h3>
              <div className="space-y-3">
                {[
                  { name: 'Sarah Ahmed', points: 2840, badge: 'Expert' },
                  { name: 'Omar Al-Rashid', points: 2156, badge: 'Mentor' },
                  { name: 'Layla Mahmoud', points: 1890, badge: 'Innovator' }
                ].map((contributor, index) => (
                  <div key={index} className="flex items-center gap-3 p-2 rounded-lg hover:bg-white/5 cursor-pointer transition-all duration-200">
                    <div className="relative">
                      <div className="w-10 h-10 bg-gradient-to-r from-purple-500 to-blue-500 rounded-full flex items-center justify-center text-white font-bold text-sm">
                        {contributor.name.charAt(0)}
                      </div>
                      {index === 0 && <Crown className="absolute -top-1 -right-1 w-4 h-4 text-yellow-400" />}
                    </div>
                    <div className="flex-1">
                      <p className="text-white font-medium text-sm">{contributor.name}</p>
                      <div className="flex items-center gap-2">
                        <span className="text-gray-400 text-xs">{contributor.points} pts</span>
                        <span className="bg-purple-500/20 text-purple-300 px-2 py-0.5 rounded text-xs">
                          {contributor.badge}
                        </span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Main Feed */}
          <div className="lg:col-span-2 space-y-6">
            {/* Create Post Quick Action */}
            <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 border border-white/10 shadow-xl">
              <div className="flex items-center gap-4">
                <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-blue-500 rounded-full flex items-center justify-center text-white font-bold shadow-lg">
                  {user?.first_name?.charAt(0) || 'U'}
                </div>
                <button
                  onClick={() => handleModalToggle('createPost', true)}
                  className="flex-1 bg-white/5 hover:bg-white/10 border border-white/20 rounded-xl px-4 py-3 text-left text-gray-400 hover:text-gray-300 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-purple-500/50"
                >
                  {isRTL ? 'ما الذي تفكر فيه؟' : 'What\'s on your mind?'}
                </button>
                <div className="flex items-center gap-2">
                  <button
                    onClick={() => handleModalToggle('createPost', true)}
                    className="p-3 text-gray-400 hover:text-green-400 hover:bg-green-500/10 rounded-xl transition-all duration-200"
                    title={isRTL ? 'إضافة صورة' : 'Add Image'}
                  >
                    <ImageIcon className="w-5 h-5" />
                  </button>
                  <button
                    onClick={() => handleModalToggle('createPost', true)}
                    className="p-3 text-gray-400 hover:text-blue-400 hover:bg-blue-500/10 rounded-xl transition-all duration-200"
                    title={isRTL ? 'إضافة فيديو' : 'Add Video'}
                  >
                    <Video className="w-5 h-5" />
                  </button>
                  <button
                    onClick={() => handleModalToggle('createPost', true)}
                    className="p-3 text-gray-400 hover:text-purple-400 hover:bg-purple-500/10 rounded-xl transition-all duration-200"
                    title={isRTL ? 'إضافة رابط' : 'Add Link'}
                  >
                    <Link className="w-5 h-5" />
                  </button>
                </div>
              </div>
            </div>

            {/* Enhanced Posts Feed with Virtual Scrolling */}
            <div className="space-y-6" ref={virtualListRef}>
              {filteredPosts.map((post: CommunityPost) => (
              <div key={post.id} className="bg-white/10 backdrop-blur-sm rounded-2xl border border-white/10 overflow-hidden hover:bg-white/15 transition-all duration-300">
                {/* Post Header */}
                <div className="p-6 pb-4">
                  <div className="flex items-start justify-between">
                    <div className="flex items-start gap-4">
                      <div className="relative">
                        <div className="w-14 h-14 bg-gradient-to-r from-purple-500 to-blue-500 rounded-full flex items-center justify-center text-white font-bold text-lg shadow-lg">
                          {post.author.firstName.charAt(0)}
                        </div>
                        {post.author.isVerified && (
                          <div className="absolute -bottom-1 -right-1 w-6 h-6 bg-yellow-500 rounded-full flex items-center justify-center shadow-lg">
                            <Verified className="w-4 h-4 text-white" />
                          </div>
                        )}
                        {post.author.role.name === 'Angel Investor' && (
                          <div className="absolute -bottom-1 -right-1 w-6 h-6 bg-green-500 rounded-full flex items-center justify-center shadow-lg">
                            <Crown className="w-4 h-4 text-white" />
                          </div>
                        )}
                        {post.author.isOnline && (
                          <div className="absolute -top-1 -right-1 w-4 h-4 bg-green-500 rounded-full border-2 border-gray-900 animate-pulse"></div>
                        )}
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-1">
                          <h4 className="font-bold text-white text-lg">{post.author.firstName} {post.author.lastName}</h4>
                          <span className="text-gray-400">•</span>
                          <span className="text-gray-400 text-sm flex items-center gap-1">
                            <Clock className="w-4 h-4" />
                            {new Date(post.createdAt).toLocaleString()}
                          </span>
                          {post.isEdited && (
                            <span className="text-gray-500 text-xs">{isRTL ? '(محرر)' : '(edited)'}</span>
                          )}
                        </div>
                        <div className="flex items-center gap-2 flex-wrap">
                          <span className={`px-3 py-1 rounded-full text-sm font-medium border ${
                            post.author.role.name === 'Tech Entrepreneur'
                              ? 'bg-gradient-to-r from-purple-500/20 to-blue-500/20 text-purple-300 border-purple-500/30'
                              : post.author.role.name === 'Angel Investor'
                              ? 'bg-gradient-to-r from-green-500/20 to-emerald-500/20 text-green-300 border-green-500/30'
                              : 'bg-gradient-to-r from-gray-500/20 to-slate-500/20 text-gray-300 border-gray-500/30'
                          }`}>
                            {post.author.role.name}
                          </span>
                          <span className="bg-white/10 text-gray-300 px-3 py-1 rounded-full text-sm border border-white/20">
                            {post.category.name}
                          </span>
                          {post.tags.slice(0, 2).map((tag) => (
                            <span key={tag.id} className="bg-purple-500/20 text-purple-300 px-2 py-1 rounded text-xs border border-purple-500/30">
                              {tag.name}
                            </span>
                          ))}
                          {post.tags.length > 2 && (
                            <span className="text-gray-400 text-xs">+{post.tags.length - 2} more</span>
                          )}
                        </div>
                      </div>
                    </div>

                    {/* Post Actions Menu */}
                    {(post.canEdit || post.canDelete) && (
                      <div className="relative group">
                        <button className="text-gray-400 hover:text-white p-2 rounded-xl hover:bg-white/10 transition-all duration-200">
                          <MoreHorizontal className="w-5 h-5" />
                        </button>
                        <div className="absolute right-0 top-full mt-2 bg-black/90 backdrop-blur-sm rounded-xl border border-white/20 py-2 min-w-[140px] opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-10 shadow-xl">
                          {post.canEdit && (
                            <button
                              onClick={() => openEditModal(post)}
                              className="w-full px-4 py-2 text-left text-gray-300 hover:text-white hover:bg-white/10 flex items-center gap-3 transition-all duration-200"
                            >
                              <Edit3 className="w-4 h-4" />
                              {isRTL ? 'تحرير' : 'Edit'}
                            </button>
                          )}
                          {post.canDelete && (
                            <button
                              onClick={() => handleDeletePost(post.id)}
                              className="w-full px-4 py-2 text-left text-red-300 hover:text-red-200 hover:bg-red-500/10 flex items-center gap-3 transition-all duration-200"
                            >
                              <Trash2 className="w-4 h-4" />
                              {isRTL ? 'حذف' : 'Delete'}
                            </button>
                          )}
                        </div>
                      </div>
                    )}
                  </div>
                </div>

                {/* Post Content */}
                <div className="px-6 pb-4">
                  <h3 className="text-xl font-bold text-white mb-3 leading-tight">{post.title}</h3>
                  <div className="text-gray-300 leading-relaxed whitespace-pre-line">
                    {post.content.raw}
                  </div>

                  {/* Media Attachments */}
                  {post.media.length > 0 && (
                    <div className="mt-4 grid grid-cols-1 md:grid-cols-2 gap-3">
                      {post.media.map((media) => (
                        <div key={media.id} className="relative rounded-xl overflow-hidden">
                          {media.type === 'image' && (
                            <img
                              src={media.url}
                              alt={media.metadata.alt || 'Post image'}
                              className="w-full h-48 object-cover hover:scale-105 transition-transform duration-300"
                            />
                          )}
                          {media.type === 'video' && (
                            <video
                              src={media.url}
                              controls
                              className="w-full h-48 object-cover rounded-xl"
                            />
                          )}
                        </div>
                      ))}
                    </div>
                  )}
                </div>

                {/* Post Engagement */}
                <div className="px-6 py-4 border-t border-white/10">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-6">
                      <button
                        onClick={() => handleLikePost(post.id)}
                        className={`flex items-center gap-2 px-4 py-2 rounded-xl transition-all duration-200 transform hover:scale-105 ${
                          post.engagement.userInteractions.isLiked
                            ? 'text-red-400 bg-red-500/20 border border-red-500/30 shadow-lg shadow-red-500/25'
                            : 'text-gray-400 hover:text-red-400 hover:bg-red-500/10 border border-transparent hover:border-red-500/30'
                        }`}
                      >
                        <Heart className={`w-5 h-5 ${post.engagement.userInteractions.isLiked ? 'fill-current' : ''}`} />
                        <span className="font-medium">{post.engagement.likes.toLocaleString()}</span>
                      </button>

                      <button className="flex items-center gap-2 px-4 py-2 rounded-xl text-gray-400 hover:text-blue-400 hover:bg-blue-500/10 border border-transparent hover:border-blue-500/30 transition-all duration-200 transform hover:scale-105">
                        <MessageSquare className="w-5 h-5" />
                        <span className="font-medium">{post.engagement.comments.toLocaleString()}</span>
                      </button>

                      <div className="flex items-center gap-2 text-gray-400">
                        <Eye className="w-5 h-5" />
                        <span className="font-medium">{post.engagement.views.toLocaleString()}</span>
                      </div>
                    </div>

                    <div className="flex items-center gap-2">
                      <button
                        onClick={() => handleSavePost(post.id)}
                        className={`p-2 rounded-xl transition-all duration-200 transform hover:scale-105 ${
                          post.engagement.userInteractions.isSaved
                            ? 'text-yellow-400 bg-yellow-500/20 border border-yellow-500/30'
                            : 'text-gray-400 hover:text-yellow-400 hover:bg-yellow-500/10'
                        }`}
                      >
                        <Bookmark className={`w-5 h-5 ${post.engagement.userInteractions.isSaved ? 'fill-current' : ''}`} />
                      </button>
                      <button
                        onClick={() => handleSharePost(post.id)}
                        className="flex items-center gap-2 px-4 py-2 rounded-xl text-gray-400 hover:text-purple-400 hover:bg-purple-500/10 border border-transparent hover:border-purple-500/30 transition-all duration-200 transform hover:scale-105"
                      >
                        <Share2 className="w-5 h-5" />
                        <span className="font-medium">{isRTL ? 'مشاركة' : 'Share'}</span>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
              ))}
            </div>
          </div>

          {/* Right Sidebar - Additional Info */}
          <div className="lg:col-span-1 space-y-6">
            {/* Upcoming Events */}
            <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 border border-white/10">
              <h3 className="text-lg font-bold text-white mb-4 flex items-center gap-2">
                <Zap className="w-5 h-5 text-yellow-400" />
                {isRTL ? 'الفعاليات القادمة' : 'Upcoming Events'}
              </h3>
              <div className="space-y-4">
                {[
                  { title: 'Startup Pitch Night', date: 'Dec 15', attendees: 234 },
                  { title: 'AI Workshop', date: 'Dec 18', attendees: 156 },
                  { title: 'Networking Mixer', date: 'Dec 22', attendees: 89 }
                ].map((event, index) => (
                  <div key={index} className="p-3 bg-white/5 rounded-xl hover:bg-white/10 cursor-pointer transition-all duration-200">
                    <h4 className="text-white font-medium text-sm">{event.title}</h4>
                    <div className="flex items-center justify-between mt-1">
                      <span className="text-gray-400 text-xs">{event.date}</span>
                      <span className="text-purple-400 text-xs">{event.attendees} attending</span>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Suggested Connections */}
            <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 border border-white/10">
              <h3 className="text-lg font-bold text-white mb-4 flex items-center gap-2">
                <Users className="w-5 h-5 text-blue-400" />
                {isRTL ? 'اتصالات مقترحة' : 'Suggested Connections'}
              </h3>
              <div className="space-y-3">
                {[
                  { name: 'Maya Chen', role: 'Product Manager', mutual: 12 },
                  { name: 'Alex Rodriguez', role: 'UX Designer', mutual: 8 },
                  { name: 'Fatima Al-Zahra', role: 'Data Scientist', mutual: 15 }
                ].map((person, index) => (
                  <div key={index} className="flex items-center justify-between p-3 bg-white/5 rounded-xl hover:bg-white/10 transition-all duration-200">
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 bg-gradient-to-r from-green-500 to-teal-500 rounded-full flex items-center justify-center text-white font-bold text-sm">
                        {person.name.charAt(0)}
                      </div>
                      <div>
                        <p className="text-white font-medium text-sm">{person.name}</p>
                        <p className="text-gray-400 text-xs">{person.role}</p>
                      </div>
                    </div>
                    <button className="bg-gradient-to-r from-purple-600 to-blue-600 text-white px-3 py-1 rounded-lg text-xs font-medium hover:shadow-lg transition-all duration-200">
                      {isRTL ? 'متابعة' : 'Follow'}
                    </button>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CommunityPage;
