/**
 * 🏠 USER HOME PAGE
 * Simple home page for regular users with minimal functionality
 * Regular users should not have access to dashboard or advanced features
 */

import React, { useState } from 'react';
import { useAuth } from '../hooks/useAuth';
import { useLanguage } from '../hooks/useLanguage';
import { useNavigate } from 'react-router-dom';
import EnhancedAIChat from '../components/ai/EnhancedAIChat';
import {
  User,
  Settings,
  Calendar,
  BookOpen,
  LogOut,
  Sparkles,
  Target,
  Zap,
  Bot,
  Users
} from 'lucide-react';

const UserHomePage: React.FC = () => {
  console.log('👤 UserHomePage rendering...');
  const { user, logout } = useAuth();
  const { isRTL } = useLanguage();
  const navigate = useNavigate();

  // Tab state
  const [activeTab, setActiveTab] = useState('profile');

  const handleLogout = async () => {
    try {
      await logout();
      navigate('/login');
    } catch (error) {
      console.error('Logout error:', error);
    }
  };



  // Tab configuration
  const tabs = [
    {
      id: 'profile',
      title: isRTL ? 'الملف الشخصي' : 'Profile',
      icon: <User className="w-5 h-5" />,
      description: isRTL ? 'عرض وتحديث معلوماتك الشخصية وإنجازاتك' : 'View and update your personal information and achievements'
    },
    {
      id: 'ai-chat',
      title: isRTL ? 'مساعد الذكاء الاصطناعي' : 'AI Assistant',
      icon: <Bot className="w-5 h-5" />,
      description: isRTL ? 'تحدث مع مساعد الذكاء الاصطناعي للحصول على المساعدة' : 'Chat with AI assistant for help and guidance'
    },
    {
      id: 'settings',
      title: isRTL ? 'الإعدادات' : 'Settings',
      icon: <Settings className="w-5 h-5" />,
      description: isRTL ? 'تخصيص تجربتك وإدارة إعدادات حسابك' : 'Customize your experience and manage account settings'
    },
    {
      id: 'events',
      title: isRTL ? 'الفعاليات' : 'Events',
      icon: <Calendar className="w-5 h-5" />,
      description: isRTL ? 'اكتشف ورش العمل والفعاليات التفاعلية' : 'Discover workshops and interactive events'
    },
    {
      id: 'resources',
      title: isRTL ? 'الموارد التعليمية' : 'Resources',
      icon: <BookOpen className="w-5 h-5" />,
      description: isRTL ? 'اطلع على المواد التعليمية والدورات المجانية' : 'Access educational materials and free courses'
    }
  ];

  return (
    <div className={`min-h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-violet-900 ${isRTL ? 'font-arabic' : ''}`}>
      {/* Modern Header with Dark Theme */}
      <div className="bg-black/30 backdrop-blur-sm shadow-lg border-b border-white/20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className={`flex items-center justify-between h-20 ${isRTL ? 'flex-row-reverse' : ''}`}>
            <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
              <div className="relative">
                <div className="absolute inset-0 bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl blur opacity-75"></div>
                <div className="relative bg-gradient-to-r from-blue-600 to-purple-600 p-2 rounded-xl">
                  <Sparkles className="w-8 h-8 text-white" />
                </div>
              </div>
              <h1 className={`text-2xl font-bold bg-gradient-to-r from-purple-400 to-blue-400 bg-clip-text text-transparent ${isRTL ? 'mr-4' : 'ml-4'}`}>
                {isRTL ? 'ياسمين AI' : 'Yasmeen AI'}
              </h1>
            </div>
            <div className={`flex items-center space-x-6 ${isRTL ? 'space-x-reverse' : ''}`}>
              <div className="flex items-center space-x-2">
                <div className="w-10 h-10 bg-gradient-to-r from-purple-500 to-blue-500 rounded-full flex items-center justify-center">
                  <User className="w-5 h-5 text-white" />
                </div>
                <div className="text-right">
                  <p className="text-sm font-medium text-white">
                    {isRTL ? 'مرحباً' : 'Welcome'}, {user?.first_name}
                  </p>
                  <p className="text-xs text-gray-300">{isRTL ? 'مستخدم' : 'User'}</p>
                </div>
              </div>
              <button
                onClick={() => navigate('/community')}
                className="flex items-center px-4 py-2 text-sm text-purple-300 hover:text-purple-200 hover:bg-purple-500/20 rounded-lg transition-all duration-200 border border-purple-500/30 hover:border-purple-400/50"
              >
                <Users className={`w-4 h-4 ${isRTL ? 'ml-2' : 'mr-2'}`} />
                {isRTL ? 'المجتمع' : 'Community'}
              </button>
              <button
                onClick={handleLogout}
                className="flex items-center px-4 py-2 text-sm text-red-300 hover:text-red-200 hover:bg-red-500/20 rounded-lg transition-all duration-200 border border-red-500/30 hover:border-red-400/50"
              >
                <LogOut className={`w-4 h-4 ${isRTL ? 'ml-2' : 'mr-2'}`} />
                {isRTL ? 'تسجيل الخروج' : 'Logout'}
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Welcome Section with Tabs */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="bg-black/30 backdrop-blur-sm rounded-2xl p-8 border border-white/20 mb-8">
          <div className={`${isRTL ? 'text-right' : 'text-left'} mb-8`}>
            <h1 className="text-2xl font-bold text-white mb-3">
              {isRTL ? `مرحباً، ${user?.first_name}!` : `Welcome back, ${user?.first_name}!`}
            </h1>
            <p className="text-gray-300 text-lg">
              {isRTL
                ? 'استخدم التبويبات أدناه للوصول إلى الميزات المختلفة.'
                : 'Use the tabs below to access different features.'
              }
            </p>
          </div>

          {/* Tab Navigation */}
          <div className="border-b border-white/20 mb-8">
            <nav className={`flex space-x-8 ${isRTL ? 'space-x-reverse' : ''}`}>
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`group relative py-4 px-1 border-b-2 font-medium text-sm transition-all duration-200 ${
                    activeTab === tab.id
                      ? 'border-purple-400 text-purple-400'
                      : 'border-transparent text-gray-300 hover:text-white hover:border-white/30'
                  }`}
                >
                  <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                    <div className={`${isRTL ? 'ml-2' : 'mr-2'} ${
                      activeTab === tab.id ? 'text-purple-400' : 'text-gray-400 group-hover:text-gray-300'
                    }`}>
                      {tab.icon}
                    </div>
                    <span>{tab.title}</span>
                  </div>
                  {activeTab === tab.id && (
                    <div className="absolute inset-x-0 bottom-0 h-0.5 bg-gradient-to-r from-purple-400 to-blue-400 rounded-full"></div>
                  )}
                </button>
              ))}
            </nav>
          </div>

          {/* Tab Content */}
          <div className="min-h-[400px]">
            {activeTab === 'profile' && (
              <div className="space-y-8">
                {/* Profile Header */}
                <div className="flex items-center justify-between mb-6">
                  <h3 className="text-2xl font-bold text-white">
                    {isRTL ? 'الملف الشخصي' : 'My Profile'}
                  </h3>
                  <button
                    onClick={() => navigate('/user/profile')}
                    className="inline-flex items-center px-4 py-2 bg-gradient-to-r from-purple-600 to-blue-600 text-white rounded-lg hover:shadow-glow transition-all duration-300"
                  >
                    <User className={`w-4 h-4 ${isRTL ? 'ml-2' : 'mr-2'}`} />
                    {isRTL ? 'تحرير الملف الشخصي' : 'Edit Profile'}
                  </button>
                </div>

                {/* Main Profile Card */}
                <div className="bg-white/10 backdrop-blur-sm rounded-xl p-8 border border-white/20">
                  <div className="flex flex-col md:flex-row items-start md:items-center gap-6">
                    {/* Profile Picture */}
                    <div className="relative">
                      <div className="w-24 h-24 bg-gradient-to-r from-purple-600 to-blue-600 rounded-full flex items-center justify-center text-2xl font-bold text-white">
                        {user?.first_name?.[0]}{user?.last_name?.[0]}
                      </div>
                      <div className="absolute -bottom-2 -right-2 w-8 h-8 bg-green-500 rounded-full border-4 border-gray-900 flex items-center justify-center">
                        <div className="w-3 h-3 bg-white rounded-full"></div>
                      </div>
                    </div>

                    {/* Basic Info */}
                    <div className="flex-1">
                      <h4 className="text-2xl font-bold text-white mb-2">
                        {user?.first_name} {user?.last_name}
                      </h4>
                      <p className="text-purple-400 font-medium mb-2">@{user?.username}</p>
                      <div className="flex flex-wrap gap-4 text-sm text-gray-300">
                        <div className="flex items-center">
                          <Calendar className="w-4 h-4 mr-2" />
                          {isRTL ? 'انضم في' : 'Joined'} {user?.date_joined ? new Date(user.date_joined).toLocaleDateString() : 'Recently'}
                        </div>
                        <div className="flex items-center">
                          <User className="w-4 h-4 mr-2" />
                          {isRTL ? 'مستخدم' : 'User'}
                        </div>
                      </div>
                    </div>

                    {/* Profile Completion */}
                    <div className="bg-white/5 rounded-lg p-4 min-w-[200px]">
                      <div className="text-center">
                        <div className="text-2xl font-bold text-white mb-1">
                          {user?.profile?.completion_percentage || 65}%
                        </div>
                        <p className="text-gray-300 text-sm mb-3">
                          {isRTL ? 'اكتمال الملف الشخصي' : 'Profile Complete'}
                        </p>
                        <div className="w-full bg-gray-700 rounded-full h-2">
                          <div
                            className="bg-gradient-to-r from-purple-500 to-blue-500 h-2 rounded-full transition-all duration-300"
                            style={{ width: `${user?.profile?.completion_percentage || 65}%` }}
                          ></div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Profile Details Grid */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {/* Contact Information */}
                  <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20">
                    <h5 className="font-semibold text-white mb-4 flex items-center">
                      <User className="w-5 h-5 mr-2 text-blue-400" />
                      {isRTL ? 'معلومات الاتصال' : 'Contact Information'}
                    </h5>
                    <div className="space-y-3">
                      <div>
                        <p className="text-gray-400 text-sm">{isRTL ? 'البريد الإلكتروني' : 'Email'}</p>
                        <p className="text-white">{user?.email}</p>
                      </div>
                      {user?.profile?.phone_number && (
                        <div>
                          <p className="text-gray-400 text-sm">{isRTL ? 'الهاتف' : 'Phone'}</p>
                          <p className="text-white">{user.profile.phone_number}</p>
                        </div>
                      )}
                      {user?.profile?.location && (
                        <div>
                          <p className="text-gray-400 text-sm">{isRTL ? 'الموقع' : 'Location'}</p>
                          <p className="text-white">{user.profile.location}</p>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Professional Information */}
                  <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20">
                    <h5 className="font-semibold text-white mb-4 flex items-center">
                      <Settings className="w-5 h-5 mr-2 text-purple-400" />
                      {isRTL ? 'المعلومات المهنية' : 'Professional Info'}
                    </h5>
                    <div className="space-y-3">
                      {user?.profile?.company && (
                        <div>
                          <p className="text-gray-400 text-sm">{isRTL ? 'الشركة' : 'Company'}</p>
                          <p className="text-white">{user.profile.company}</p>
                        </div>
                      )}
                      {user?.profile?.job_title && (
                        <div>
                          <p className="text-gray-400 text-sm">{isRTL ? 'المسمى الوظيفي' : 'Job Title'}</p>
                          <p className="text-white">{user.profile.job_title}</p>
                        </div>
                      )}
                      {user?.profile?.industry && (
                        <div>
                          <p className="text-gray-400 text-sm">{isRTL ? 'الصناعة' : 'Industry'}</p>
                          <p className="text-white">{user.profile.industry}</p>
                        </div>
                      )}
                      {user?.profile?.experience_years && (
                        <div>
                          <p className="text-gray-400 text-sm">{isRTL ? 'سنوات الخبرة' : 'Experience'}</p>
                          <p className="text-white">{user.profile.experience_years} {isRTL ? 'سنوات' : 'years'}</p>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Social Links */}
                  <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20">
                    <h5 className="font-semibold text-white mb-4 flex items-center">
                      <BookOpen className="w-5 h-5 mr-2 text-green-400" />
                      {isRTL ? 'الروابط الاجتماعية' : 'Social Links'}
                    </h5>
                    <div className="space-y-3">
                      {user?.profile?.website && (
                        <div>
                          <p className="text-gray-400 text-sm">{isRTL ? 'الموقع الإلكتروني' : 'Website'}</p>
                          <a href={user.profile.website} target="_blank" rel="noopener noreferrer" className="text-blue-400 hover:text-blue-300 transition-colors">
                            {user.profile.website}
                          </a>
                        </div>
                      )}
                      {user?.profile?.linkedin_url && (
                        <div>
                          <p className="text-gray-400 text-sm">LinkedIn</p>
                          <a href={user.profile.linkedin_url} target="_blank" rel="noopener noreferrer" className="text-blue-400 hover:text-blue-300 transition-colors">
                            LinkedIn Profile
                          </a>
                        </div>
                      )}
                      {user?.profile?.github_url && (
                        <div>
                          <p className="text-gray-400 text-sm">GitHub</p>
                          <a href={user.profile.github_url} target="_blank" rel="noopener noreferrer" className="text-blue-400 hover:text-blue-300 transition-colors">
                            GitHub Profile
                          </a>
                        </div>
                      )}
                    </div>
                  </div>
                </div>

                {/* Bio Section */}
                {user?.profile?.bio && (
                  <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20">
                    <h5 className="font-semibold text-white mb-4 flex items-center">
                      <User className="w-5 h-5 mr-2 text-yellow-400" />
                      {isRTL ? 'نبذة شخصية' : 'About Me'}
                    </h5>
                    <p className="text-gray-300 leading-relaxed">{user.profile.bio}</p>
                  </div>
                )}

                {/* Achievements and Stats */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20">
                    <h5 className="font-semibold text-white mb-4 flex items-center">
                      <Target className="w-5 h-5 mr-2 text-green-400" />
                      {isRTL ? 'الإنجازات' : 'Achievements'}
                    </h5>
                    <div className="space-y-3">
                      <div className="flex items-center">
                        <div className="w-3 h-3 bg-green-400 rounded-full mr-3"></div>
                        <span className="text-gray-300 text-sm">
                          {isRTL ? 'عضو مسجل' : 'Registered Member'}
                        </span>
                      </div>
                      <div className="flex items-center">
                        <div className="w-3 h-3 bg-blue-400 rounded-full mr-3"></div>
                        <span className="text-gray-300 text-sm">
                          {isRTL ? 'ملف شخصي نشط' : 'Active Profile'}
                        </span>
                      </div>
                      {user?.profile?.email_notifications && (
                        <div className="flex items-center">
                          <div className="w-3 h-3 bg-purple-400 rounded-full mr-3"></div>
                          <span className="text-gray-300 text-sm">
                            {isRTL ? 'الإشعارات مفعلة' : 'Notifications Enabled'}
                          </span>
                        </div>
                      )}
                    </div>
                  </div>

                  <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20">
                    <h5 className="font-semibold text-white mb-4 flex items-center">
                      <Settings className="w-5 h-5 mr-2 text-blue-400" />
                      {isRTL ? 'إعدادات الحساب' : 'Account Settings'}
                    </h5>
                    <div className="space-y-3">
                      <div className="flex items-center justify-between">
                        <span className="text-gray-300 text-sm">{isRTL ? 'حالة الحساب' : 'Account Status'}</span>
                        <span className="text-green-400 font-medium">{isRTL ? 'نشط' : 'Active'}</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-gray-300 text-sm">{isRTL ? 'اللغة' : 'Language'}</span>
                        <span className="text-purple-400 font-medium">{user?.profile?.language === 'ar' ? 'العربية' : 'English'}</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-gray-300 text-sm">{isRTL ? 'الخصوصية' : 'Privacy'}</span>
                        <span className="text-blue-400 font-medium">
                          {user?.profile?.profile_visibility === 'public' ? (isRTL ? 'عام' : 'Public') :
                           user?.profile?.profile_visibility === 'members' ? (isRTL ? 'الأعضاء فقط' : 'Members Only') :
                           (isRTL ? 'خاص' : 'Private')}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'ai-chat' && (
              <div className="space-y-6">
                <div className="flex items-center justify-between mb-6">
                  <h3 className="text-2xl font-bold text-white">
                    {isRTL ? 'مساعد الذكاء الاصطناعي' : 'AI Assistant'}
                  </h3>
                  <div className="flex items-center space-x-2">
                    <div className="w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
                    <span className="text-green-400 text-sm font-medium">
                      {isRTL ? 'متصل' : 'Online'}
                    </span>
                  </div>
                </div>

                {/* AI Chat Interface */}
                <div className="bg-white/10 backdrop-blur-sm rounded-xl border border-white/20 overflow-hidden">
                  <div className="p-4 border-b border-white/20 bg-white/5">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <div className="w-10 h-10 bg-gradient-to-r from-purple-500 to-blue-500 rounded-full flex items-center justify-center">
                          <Bot className="w-6 h-6 text-white" />
                        </div>
                        <div>
                          <h4 className="font-semibold text-white">
                            {isRTL ? 'ياسمين - مساعدك الذكي' : 'Yasmeen - Your AI Assistant'}
                          </h4>
                          <p className="text-gray-300 text-sm">
                            {isRTL ? 'اسأل أي سؤال وسأساعدك' : 'Ask me anything and I\'ll help you'}
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Enhanced AI Chat Component */}
                  <div className="h-[500px]">
                    <EnhancedAIChat
                      height="h-full"
                      className="w-full"
                      showFeatures={true}
                      chatType="general"
                    />
                  </div>
                </div>

                {/* AI Features Info */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="bg-white/5 rounded-lg p-4 text-center">
                    <Bot className="w-8 h-8 text-blue-400 mx-auto mb-2" />
                    <h5 className="font-medium text-white mb-1">
                      {isRTL ? 'محادثة ذكية' : 'Smart Chat'}
                    </h5>
                    <p className="text-gray-300 text-sm">
                      {isRTL ? 'تحدث بالعربية أو الإنجليزية' : 'Chat in Arabic or English'}
                    </p>
                  </div>
                  <div className="bg-white/5 rounded-lg p-4 text-center">
                    <Sparkles className="w-8 h-8 text-purple-400 mx-auto mb-2" />
                    <h5 className="font-medium text-white mb-1">
                      {isRTL ? 'مساعدة فورية' : 'Instant Help'}
                    </h5>
                    <p className="text-gray-300 text-sm">
                      {isRTL ? 'احصل على إجابات سريعة' : 'Get quick answers'}
                    </p>
                  </div>
                  <div className="bg-white/5 rounded-lg p-4 text-center">
                    <Target className="w-8 h-8 text-green-400 mx-auto mb-2" />
                    <h5 className="font-medium text-white mb-1">
                      {isRTL ? 'نصائح مخصصة' : 'Personalized Tips'}
                    </h5>
                    <p className="text-gray-300 text-sm">
                      {isRTL ? 'نصائح مناسبة لك' : 'Tips tailored for you'}
                    </p>
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'settings' && (
              <div className="space-y-6">
                <div className="flex items-center justify-between mb-6">
                  <h3 className="text-2xl font-bold text-white">
                    {isRTL ? 'الإعدادات' : 'Settings'}
                  </h3>
                  <button
                    onClick={() => navigate('/user/settings')}
                    className="inline-flex items-center px-4 py-2 bg-gradient-to-r from-gray-600 to-gray-700 text-white rounded-lg hover:shadow-glow transition-all duration-300"
                  >
                    <Settings className={`w-4 h-4 ${isRTL ? 'ml-2' : 'mr-2'}`} />
                    {isRTL ? 'إعدادات متقدمة' : 'Advanced Settings'}
                  </button>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20">
                    <h4 className="font-semibold text-white mb-4">
                      {isRTL ? 'إعدادات الحساب' : 'Account Settings'}
                    </h4>
                    <div className="space-y-3">
                      <div className="flex items-center justify-between">
                        <span className="text-gray-300">{isRTL ? 'اللغة' : 'Language'}</span>
                        <span className="text-purple-400 font-medium">{isRTL ? 'العربية' : 'Arabic'}</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-gray-300">{isRTL ? 'المنطقة الزمنية' : 'Timezone'}</span>
                        <span className="text-purple-400 font-medium">GMT+3</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-gray-300">{isRTL ? 'الإشعارات' : 'Notifications'}</span>
                        <span className="text-green-400 font-medium">{isRTL ? 'مفعلة' : 'Enabled'}</span>
                      </div>
                    </div>
                  </div>

                  <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20">
                    <h4 className="font-semibold text-white mb-4">
                      {isRTL ? 'إعدادات الخصوصية' : 'Privacy Settings'}
                    </h4>
                    <div className="space-y-3">
                      <div className="flex items-center justify-between">
                        <span className="text-gray-300">{isRTL ? 'الملف العام' : 'Public Profile'}</span>
                        <span className="text-green-400 font-medium">{isRTL ? 'مرئي' : 'Visible'}</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-gray-300">{isRTL ? 'البريد الإلكتروني' : 'Email'}</span>
                        <span className="text-orange-400 font-medium">{isRTL ? 'خاص' : 'Private'}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'events' && (
              <div className="space-y-6">
                <div className="flex items-center justify-between mb-6">
                  <h3 className="text-2xl font-bold text-white">
                    {isRTL ? 'الفعاليات' : 'Events'}
                  </h3>
                  <button
                    onClick={() => navigate('/user/events')}
                    className="inline-flex items-center px-4 py-2 bg-gradient-to-r from-green-600 to-green-700 text-white rounded-lg hover:shadow-glow transition-all duration-300"
                  >
                    <Calendar className={`w-4 h-4 ${isRTL ? 'ml-2' : 'mr-2'}`} />
                    {isRTL ? 'عرض جميع الفعاليات' : 'View All Events'}
                  </button>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20">
                    <div className="flex items-center mb-4">
                      <Calendar className="w-8 h-8 text-green-400" />
                      <h4 className={`font-semibold text-white ${isRTL ? 'mr-3' : 'ml-3'}`}>
                        {isRTL ? 'الفعاليات القادمة' : 'Upcoming Events'}
                      </h4>
                    </div>
                    <p className="text-gray-300 mb-4">
                      {isRTL
                        ? 'اكتشف ورش العمل والفعاليات التفاعلية المصممة خصيصاً لك.'
                        : 'Discover workshops and interactive events designed specifically for you.'
                      }
                    </p>
                    <div className="text-sm text-gray-400">
                      {isRTL ? 'لا توجد فعاليات مجدولة حالياً' : 'No events scheduled currently'}
                    </div>
                  </div>

                  <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20">
                    <div className="flex items-center mb-4">
                      <BookOpen className="w-8 h-8 text-blue-400" />
                      <h4 className={`font-semibold text-white ${isRTL ? 'mr-3' : 'ml-3'}`}>
                        {isRTL ? 'ورش العمل' : 'Workshops'}
                      </h4>
                    </div>
                    <p className="text-gray-300 mb-4">
                      {isRTL
                        ? 'انضم إلى ورش العمل التعليمية لتطوير مهاراتك.'
                        : 'Join educational workshops to develop your skills.'
                      }
                    </p>
                    <div className="text-sm text-gray-400">
                      {isRTL ? 'ورش عمل جديدة قريباً' : 'New workshops coming soon'}
                    </div>
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'resources' && (
              <div className="space-y-6">
                <div className="flex items-center justify-between mb-6">
                  <h3 className="text-2xl font-bold text-white">
                    {isRTL ? 'الموارد التعليمية' : 'Learning Resources'}
                  </h3>
                  <button
                    onClick={() => navigate('/user/resources')}
                    className="inline-flex items-center px-4 py-2 bg-gradient-to-r from-purple-600 to-purple-700 text-white rounded-lg hover:shadow-glow transition-all duration-300"
                  >
                    <BookOpen className={`w-4 h-4 ${isRTL ? 'ml-2' : 'mr-2'}`} />
                    {isRTL ? 'تصفح المكتبة' : 'Browse Library'}
                  </button>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20">
                    <BookOpen className="w-8 h-8 text-purple-400 mb-3" />
                    <h4 className="font-semibold text-white mb-2">
                      {isRTL ? 'الدورات المجانية' : 'Free Courses'}
                    </h4>
                    <p className="text-gray-300 text-sm">
                      {isRTL
                        ? 'اطلع على مجموعة من الدورات التعليمية المجانية'
                        : 'Access a collection of free educational courses'
                      }
                    </p>
                  </div>

                  <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20">
                    <User className="w-8 h-8 text-blue-400 mb-3" />
                    <h4 className="font-semibold text-white mb-2">
                      {isRTL ? 'أدلة المستخدم' : 'User Guides'}
                    </h4>
                    <p className="text-gray-300 text-sm">
                      {isRTL
                        ? 'تعلم كيفية استخدام المنصة بفعالية'
                        : 'Learn how to use the platform effectively'
                      }
                    </p>
                  </div>

                  <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20">
                    <Target className="w-8 h-8 text-green-400 mb-3" />
                    <h4 className="font-semibold text-white mb-2">
                      {isRTL ? 'مسارات التعلم' : 'Learning Paths'}
                    </h4>
                    <p className="text-gray-300 text-sm">
                      {isRTL
                        ? 'مسارات تعليمية منظمة لتطوير مهاراتك'
                        : 'Structured learning paths to develop your skills'
                      }
                    </p>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Call to Action Section */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="relative">
          <div className="absolute inset-0 bg-gradient-to-r from-purple-600 to-blue-600 rounded-3xl blur opacity-30"></div>
          <div className="relative bg-gradient-to-r from-purple-600 to-blue-600 rounded-3xl p-12 text-center text-white border border-white/20">
            <div className="max-w-3xl mx-auto">
              <div className="inline-flex items-center justify-center w-16 h-16 bg-white/20 rounded-2xl mb-6">
                <Target className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-3xl font-bold mb-4">
                {isRTL ? 'هل تريد المزيد من الميزات؟' : 'Ready for More Features?'}
              </h3>
              <p className="text-xl text-purple-100 mb-8 leading-relaxed">
                {isRTL
                  ? 'انضم إلى مجتمع رواد الأعمال والمستثمرين والمرشدين. اكتشف فرصاً جديدة وطور مهاراتك مع خبراء المجال.'
                  : 'Join our community of entrepreneurs, investors, and mentors. Discover new opportunities and develop your skills with industry experts.'
                }
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <button
                  onClick={() => navigate('/register')}
                  className="inline-flex items-center px-8 py-4 bg-white text-purple-600 font-semibold rounded-xl hover:bg-gray-100 transition-all duration-200 shadow-lg hover:shadow-xl"
                >
                  <Zap className={`w-5 h-5 ${isRTL ? 'ml-2' : 'mr-2'}`} />
                  {isRTL ? 'تقدم للحصول على دور متقدم' : 'Apply for Advanced Role'}
                </button>
                {process.env.NODE_ENV === 'development' && (
                  <button
                    onClick={() => navigate('/role-test')}
                    className="inline-flex items-center px-6 py-4 bg-white/20 text-white font-medium rounded-xl hover:bg-white/30 transition-all duration-200 border border-white/30"
                  >
                    🧪 Test Roles (Dev)
                  </button>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default UserHomePage;
