import React, { useState, useEffect } from 'react';
import { FileText, Clock, CheckCircle, XCircle, Eye, Plus } from 'lucide-react';

interface Application {
  id: string;
  companyName: string;
  applicantName: string;
  requestedAmount: number;
  submissionDate: string;
  status: 'pending' | 'under_review' | 'approved' | 'rejected';
  industry: string;
  description: string;
}

const ApplicationsPage: React.FC = () => {
  const [applications, setApplications] = useState<Application[]>([]);
  const [loading, setLoading] = useState(true);
  const [filter, setFilter] = useState<string>('all');

  useEffect(() => {
    // Mock data for now
    const mockApplications: Application[] = [
      {
        id: '1',
        companyName: 'TechStart Syria',
        applicantName: 'Ahmed Hassan',
        requestedAmount: 50000,
        submissionDate: '2024-01-15',
        status: 'pending',
        industry: 'Technology',
        description: 'AI-powered platform for connecting Syrian entrepreneurs with global mentors'
      },
      {
        id: '2',
        companyName: 'GreenEnergy Solutions',
        applicantName: 'Fatima Al-Zah<PERSON>',
        requestedAmount: 100000,
        submissionDate: '2024-01-10',
        status: 'under_review',
        industry: 'Clean Energy',
        description: 'Solar energy solutions for rural communities in Syria'
      },
      {
        id: '3',
        companyName: 'HealthTech Innovations',
        applicantName: 'Omar Mahmoud',
        requestedAmount: 75000,
        submissionDate: '2024-01-05',
        status: 'approved',
        industry: 'Healthcare',
        description: 'Telemedicine platform for remote healthcare delivery'
      },
      {
        id: '4',
        companyName: 'EduTech Platform',
        applicantName: 'Layla Ahmed',
        requestedAmount: 30000,
        submissionDate: '2024-01-01',
        status: 'rejected',
        industry: 'Education',
        description: 'Online learning platform for Syrian students'
      }
    ];

    setTimeout(() => {
      setApplications(mockApplications);
      setLoading(false);
    }, 1000);
  }, []);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'approved':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'rejected':
        return <XCircle className="w-5 h-5 text-red-500" />;
      case 'under_review':
        return <Clock className="w-5 h-5 text-blue-500" />;
      default:
        return <FileText className="w-5 h-5 text-gray-400" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved':
        return 'bg-green-100 text-green-800';
      case 'rejected':
        return 'bg-red-100 text-red-800';
      case 'under_review':
        return 'bg-blue-100 text-blue-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const filteredApplications = filter === 'all' 
    ? applications 
    : applications.filter(app => app.status === filter);

  const statusCounts = {
    all: applications.length,
    pending: applications.filter(app => app.status === 'pending').length,
    under_review: applications.filter(app => app.status === 'under_review').length,
    approved: applications.filter(app => app.status === 'approved').length,
    rejected: applications.filter(app => app.status === 'rejected').length
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Funding Applications</h1>
          <p className="text-gray-600">Review and manage funding applications</p>
        </div>
        <button className="bg-blue-600 text-white px-4 py-2 rounded-lg flex items-center gap-2 hover:bg-blue-700">
          <Plus className="w-4 h-4" />
          New Application
        </button>
      </div>

      {/* Filter Tabs */}
      <div className="flex space-x-1 mb-6 bg-gray-100 p-1 rounded-lg">
        {[
          { key: 'all', label: 'All' },
          { key: 'pending', label: 'Pending' },
          { key: 'under_review', label: 'Under Review' },
          { key: 'approved', label: 'Approved' },
          { key: 'rejected', label: 'Rejected' }
        ].map((tab) => (
          <button
            key={tab.key}
            onClick={() => setFilter(tab.key)}
            className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
              filter === tab.key
                ? 'bg-white text-blue-600 shadow-sm'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            {tab.label} ({statusCounts[tab.key as keyof typeof statusCounts]})
          </button>
        ))}
      </div>

      {/* Applications List */}
      <div className="space-y-4">
        {filteredApplications.map((application) => (
          <div key={application.id} className="bg-white rounded-lg shadow-md p-6 border border-gray-200">
            <div className="flex items-start justify-between mb-4">
              <div className="flex items-center gap-3">
                {getStatusIcon(application.status)}
                <div>
                  <h3 className="text-lg font-semibold text-gray-900">{application.companyName}</h3>
                  <p className="text-gray-600">by {application.applicantName}</p>
                </div>
              </div>
              <div className="flex items-center gap-3">
                <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(application.status)}`}>
                  {application.status.replace('_', ' ').toUpperCase()}
                </span>
                <button className="text-blue-600 hover:text-blue-800">
                  <Eye className="w-5 h-5" />
                </button>
              </div>
            </div>

            <p className="text-gray-700 mb-4">{application.description}</p>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
              <div>
                <span className="text-gray-500">Requested Amount:</span>
                <p className="font-semibold text-gray-900">{formatCurrency(application.requestedAmount)}</p>
              </div>
              <div>
                <span className="text-gray-500">Industry:</span>
                <p className="font-semibold text-gray-900">{application.industry}</p>
              </div>
              <div>
                <span className="text-gray-500">Submitted:</span>
                <p className="font-semibold text-gray-900">{new Date(application.submissionDate).toLocaleDateString()}</p>
              </div>
            </div>

            {application.status === 'pending' && (
              <div className="flex gap-2 mt-4 pt-4 border-t border-gray-200">
                <button className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 text-sm">
                  Approve
                </button>
                <button className="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 text-sm">
                  Reject
                </button>
                <button className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 text-sm">
                  Review
                </button>
              </div>
            )}
          </div>
        ))}
      </div>

      {filteredApplications.length === 0 && (
        <div className="text-center py-12">
          <FileText className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No applications found</h3>
          <p className="text-gray-600 mb-4">
            {filter === 'all' 
              ? 'No funding applications have been submitted yet.' 
              : `No applications with status "${filter.replace('_', ' ')}" found.`}
          </p>
          {filter !== 'all' && (
            <button 
              onClick={() => setFilter('all')}
              className="text-blue-600 hover:text-blue-800"
            >
              View all applications
            </button>
          )}
        </div>
      )}
    </div>
  );
};

export default ApplicationsPage;
