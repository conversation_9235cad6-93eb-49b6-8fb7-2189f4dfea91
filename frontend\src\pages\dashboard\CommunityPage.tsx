import React, { useState, useEffect } from 'react';
import { Users, MessageCircle, Heart, Share2, Plus } from 'lucide-react';

interface CommunityPost {
  id: string;
  author: string;
  avatar: string;
  content: string;
  timestamp: string;
  likes: number;
  comments: number;
  category: string;
}

const CommunityPage: React.FC = () => {
  const [posts, setPosts] = useState<CommunityPost[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Mock data for now
    const mockPosts: CommunityPost[] = [
      {
        id: '1',
        author: '<PERSON>',
        avatar: '/api/placeholder/40/40',
        content: 'Just launched my first startup! Looking for feedback from the community. It\'s a platform connecting Syrian entrepreneurs with mentors worldwide.',
        timestamp: '2 hours ago',
        likes: 24,
        comments: 8,
        category: 'Startup Launch'
      },
      {
        id: '2',
        author: '<PERSON>',
        avatar: '/api/placeholder/40/40',
        content: 'Sharing some insights from my recent pitch to investors. The key is to focus on the problem you\'re solving, not just the solution.',
        timestamp: '5 hours ago',
        likes: 18,
        comments: 12,
        category: 'Tips & Advice'
      },
      {
        id: '3',
        author: '<PERSON>',
        avatar: '/api/placeholder/40/40',
        content: 'Looking for a co-founder with technical background for my fintech startup. DM me if interested!',
        timestamp: '1 day ago',
        likes: 15,
        comments: 6,
        category: 'Collaboration'
      }
    ];

    setTimeout(() => {
      setPosts(mockPosts);
      setLoading(false);
    }, 1000);
  }, []);

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Community</h1>
          <p className="text-gray-600">Connect with fellow entrepreneurs and share your journey</p>
        </div>
        <button className="bg-blue-600 text-white px-4 py-2 rounded-lg flex items-center gap-2 hover:bg-blue-700">
          <Plus className="w-4 h-4" />
          New Post
        </button>
      </div>

      {/* Community Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <div className="bg-white rounded-lg shadow-md p-6 border border-gray-200">
          <div className="flex items-center gap-3">
            <Users className="w-8 h-8 text-blue-600" />
            <div>
              <h3 className="text-lg font-semibold text-gray-900">1,247</h3>
              <p className="text-gray-600">Community Members</p>
            </div>
          </div>
        </div>
        <div className="bg-white rounded-lg shadow-md p-6 border border-gray-200">
          <div className="flex items-center gap-3">
            <MessageCircle className="w-8 h-8 text-green-600" />
            <div>
              <h3 className="text-lg font-semibold text-gray-900">3,456</h3>
              <p className="text-gray-600">Discussions</p>
            </div>
          </div>
        </div>
        <div className="bg-white rounded-lg shadow-md p-6 border border-gray-200">
          <div className="flex items-center gap-3">
            <Heart className="w-8 h-8 text-red-600" />
            <div>
              <h3 className="text-lg font-semibold text-gray-900">12,890</h3>
              <p className="text-gray-600">Likes Given</p>
            </div>
          </div>
        </div>
      </div>

      {/* Posts Feed */}
      <div className="space-y-6">
        {posts.map((post) => (
          <div key={post.id} className="bg-white rounded-lg shadow-md p-6 border border-gray-200">
            <div className="flex items-start gap-4">
              <img 
                src={post.avatar} 
                alt={post.author}
                className="w-10 h-10 rounded-full bg-gray-300"
              />
              <div className="flex-1">
                <div className="flex items-center gap-2 mb-2">
                  <h4 className="font-semibold text-gray-900">{post.author}</h4>
                  <span className="text-gray-500">•</span>
                  <span className="text-gray-500 text-sm">{post.timestamp}</span>
                  <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs">
                    {post.category}
                  </span>
                </div>
                <p className="text-gray-700 mb-4">{post.content}</p>
                <div className="flex items-center gap-6">
                  <button className="flex items-center gap-2 text-gray-500 hover:text-red-500">
                    <Heart className="w-4 h-4" />
                    <span>{post.likes}</span>
                  </button>
                  <button className="flex items-center gap-2 text-gray-500 hover:text-blue-500">
                    <MessageCircle className="w-4 h-4" />
                    <span>{post.comments}</span>
                  </button>
                  <button className="flex items-center gap-2 text-gray-500 hover:text-green-500">
                    <Share2 className="w-4 h-4" />
                    <span>Share</span>
                  </button>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {posts.length === 0 && (
        <div className="text-center py-12">
          <Users className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No posts yet</h3>
          <p className="text-gray-600 mb-4">Be the first to share something with the community!</p>
          <button className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700">
            Create First Post
          </button>
        </div>
      )}
    </div>
  );
};

export default CommunityPage;
