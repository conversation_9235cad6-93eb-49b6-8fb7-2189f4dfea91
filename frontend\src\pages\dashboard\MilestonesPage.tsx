import React, { useState, useEffect } from 'react';
import { Target, Plus, CheckCircle, Clock, AlertCircle } from 'lucide-react';

interface Milestone {
  id: string;
  title: string;
  description: string;
  targetDate: string;
  status: 'pending' | 'in_progress' | 'completed' | 'overdue';
  progress: number;
  category: string;
}

const MilestonesPage: React.FC = () => {
  const [milestones, setMilestones] = useState<Milestone[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Mock data for now
    const mockMilestones: Milestone[] = [
      {
        id: '1',
        title: 'Complete Business Plan',
        description: 'Finalize the comprehensive business plan document',
        targetDate: '2024-02-15',
        status: 'in_progress',
        progress: 75,
        category: 'Planning'
      },
      {
        id: '2',
        title: 'Secure Initial Funding',
        description: 'Raise $50,000 in seed funding',
        targetDate: '2024-03-01',
        status: 'pending',
        progress: 25,
        category: 'Funding'
      },
      {
        id: '3',
        title: 'Launch MVP',
        description: 'Release minimum viable product to beta users',
        targetDate: '2024-04-01',
        status: 'pending',
        progress: 10,
        category: 'Development'
      }
    ];

    setTimeout(() => {
      setMilestones(mockMilestones);
      setLoading(false);
    }, 1000);
  }, []);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'in_progress':
        return <Clock className="w-5 h-5 text-blue-500" />;
      case 'overdue':
        return <AlertCircle className="w-5 h-5 text-red-500" />;
      default:
        return <Target className="w-5 h-5 text-gray-400" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'in_progress':
        return 'bg-blue-100 text-blue-800';
      case 'overdue':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Milestones</h1>
          <p className="text-gray-600">Track your business goals and achievements</p>
        </div>
        <button className="bg-blue-600 text-white px-4 py-2 rounded-lg flex items-center gap-2 hover:bg-blue-700">
          <Plus className="w-4 h-4" />
          Add Milestone
        </button>
      </div>

      <div className="grid gap-6">
        {milestones.map((milestone) => (
          <div key={milestone.id} className="bg-white rounded-lg shadow-md p-6 border border-gray-200">
            <div className="flex items-start justify-between mb-4">
              <div className="flex items-center gap-3">
                {getStatusIcon(milestone.status)}
                <div>
                  <h3 className="text-lg font-semibold text-gray-900">{milestone.title}</h3>
                  <p className="text-gray-600">{milestone.description}</p>
                </div>
              </div>
              <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(milestone.status)}`}>
                {milestone.status.replace('_', ' ').toUpperCase()}
              </span>
            </div>

            <div className="mb-4">
              <div className="flex justify-between text-sm text-gray-600 mb-1">
                <span>Progress</span>
                <span>{milestone.progress}%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${milestone.progress}%` }}
                ></div>
              </div>
            </div>

            <div className="flex justify-between items-center text-sm text-gray-500">
              <span className="bg-gray-100 px-2 py-1 rounded">{milestone.category}</span>
              <span>Target: {new Date(milestone.targetDate).toLocaleDateString()}</span>
            </div>
          </div>
        ))}
      </div>

      {milestones.length === 0 && (
        <div className="text-center py-12">
          <Target className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No milestones yet</h3>
          <p className="text-gray-600 mb-4">Start tracking your business goals by creating your first milestone.</p>
          <button className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700">
            Create First Milestone
          </button>
        </div>
      )}
    </div>
  );
};

export default MilestonesPage;
