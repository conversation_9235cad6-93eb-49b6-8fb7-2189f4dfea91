import React, { useState, useEffect } from 'react';
import { Search, Filter, DollarSign, Calendar, MapPin, TrendingUp } from 'lucide-react';

interface Opportunity {
  id: string;
  companyName: string;
  industry: string;
  fundingRound: string;
  amountSeeking: number;
  valuation: number;
  location: string;
  description: string;
  deadline: string;
  stage: string;
  riskLevel: 'low' | 'medium' | 'high';
}

const OpportunitiesPage: React.FC = () => {
  const [opportunities, setOpportunities] = useState<Opportunity[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterIndustry, setFilterIndustry] = useState('all');

  useEffect(() => {
    // Mock data for now
    const mockOpportunities: Opportunity[] = [
      {
        id: '1',
        companyName: 'TechStart Syria',
        industry: 'Technology',
        fundingRound: 'Series A',
        amountSeeking: 500000,
        valuation: 2000000,
        location: 'Damascus, Syria',
        description: 'AI-powered platform connecting Syrian entrepreneurs with global mentors and investors.',
        deadline: '2024-03-15',
        stage: 'Growth',
        riskLevel: 'medium'
      },
      {
        id: '2',
        companyName: 'GreenEnergy Solutions',
        industry: 'Clean Energy',
        fundingRound: 'Seed',
        amountSeeking: 250000,
        valuation: 1000000,
        location: 'Aleppo, Syria',
        description: 'Solar energy solutions for rural communities with innovative financing models.',
        deadline: '2024-02-28',
        stage: 'Early',
        riskLevel: 'high'
      },
      {
        id: '3',
        companyName: 'HealthTech Innovations',
        industry: 'Healthcare',
        fundingRound: 'Series B',
        amountSeeking: 1000000,
        valuation: 5000000,
        location: 'Latakia, Syria',
        description: 'Telemedicine platform serving underserved communities across the Middle East.',
        deadline: '2024-04-10',
        stage: 'Expansion',
        riskLevel: 'low'
      }
    ];

    setTimeout(() => {
      setOpportunities(mockOpportunities);
      setLoading(false);
    }, 1000);
  }, []);

  const getRiskColor = (risk: string) => {
    switch (risk) {
      case 'low':
        return 'bg-green-100 text-green-800';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800';
      case 'high':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  const filteredOpportunities = opportunities.filter(opp => {
    const matchesSearch = opp.companyName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         opp.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesIndustry = filterIndustry === 'all' || opp.industry === filterIndustry;
    return matchesSearch && matchesIndustry;
  });

  const industries = ['all', ...Array.from(new Set(opportunities.map(opp => opp.industry)))];

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Investment Opportunities</h1>
          <p className="text-gray-600">Discover promising startups seeking funding</p>
        </div>
      </div>

      {/* Search and Filter */}
      <div className="flex flex-col md:flex-row gap-4 mb-6">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
          <input
            type="text"
            placeholder="Search opportunities..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>
        <div className="relative">
          <Filter className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
          <select
            value={filterIndustry}
            onChange={(e) => setFilterIndustry(e.target.value)}
            className="pl-10 pr-8 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            {industries.map(industry => (
              <option key={industry} value={industry}>
                {industry === 'all' ? 'All Industries' : industry}
              </option>
            ))}
          </select>
        </div>
      </div>

      {/* Opportunities Grid */}
      <div className="grid gap-6">
        {filteredOpportunities.map((opportunity) => (
          <div key={opportunity.id} className="bg-white rounded-lg shadow-md p-6 border border-gray-200 hover:shadow-lg transition-shadow">
            <div className="flex justify-between items-start mb-4">
              <div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">{opportunity.companyName}</h3>
                <div className="flex items-center gap-4 text-sm text-gray-600">
                  <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded">{opportunity.industry}</span>
                  <span className="bg-purple-100 text-purple-800 px-2 py-1 rounded">{opportunity.fundingRound}</span>
                  <span className={`px-2 py-1 rounded ${getRiskColor(opportunity.riskLevel)}`}>
                    {opportunity.riskLevel.toUpperCase()} RISK
                  </span>
                </div>
              </div>
              <div className="text-right">
                <p className="text-2xl font-bold text-gray-900">{formatCurrency(opportunity.amountSeeking)}</p>
                <p className="text-sm text-gray-600">Seeking</p>
              </div>
            </div>

            <p className="text-gray-700 mb-4">{opportunity.description}</p>

            <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
              <div className="flex items-center gap-2">
                <DollarSign className="w-4 h-4 text-gray-400" />
                <div>
                  <p className="text-sm text-gray-600">Valuation</p>
                  <p className="font-semibold">{formatCurrency(opportunity.valuation)}</p>
                </div>
              </div>
              <div className="flex items-center gap-2">
                <TrendingUp className="w-4 h-4 text-gray-400" />
                <div>
                  <p className="text-sm text-gray-600">Stage</p>
                  <p className="font-semibold">{opportunity.stage}</p>
                </div>
              </div>
              <div className="flex items-center gap-2">
                <MapPin className="w-4 h-4 text-gray-400" />
                <div>
                  <p className="text-sm text-gray-600">Location</p>
                  <p className="font-semibold">{opportunity.location}</p>
                </div>
              </div>
              <div className="flex items-center gap-2">
                <Calendar className="w-4 h-4 text-gray-400" />
                <div>
                  <p className="text-sm text-gray-600">Deadline</p>
                  <p className="font-semibold">{new Date(opportunity.deadline).toLocaleDateString()}</p>
                </div>
              </div>
            </div>

            <div className="flex gap-3">
              <button className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex-1">
                View Details
              </button>
              <button className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 flex-1">
                Express Interest
              </button>
            </div>
          </div>
        ))}
      </div>

      {filteredOpportunities.length === 0 && (
        <div className="text-center py-12">
          <TrendingUp className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No opportunities found</h3>
          <p className="text-gray-600 mb-4">
            {searchTerm || filterIndustry !== 'all' 
              ? 'Try adjusting your search criteria.' 
              : 'No investment opportunities are currently available.'}
          </p>
          {(searchTerm || filterIndustry !== 'all') && (
            <button 
              onClick={() => {
                setSearchTerm('');
                setFilterIndustry('all');
              }}
              className="text-blue-600 hover:text-blue-800"
            >
              Clear filters
            </button>
          )}
        </div>
      )}
    </div>
  );
};

export default OpportunitiesPage;
