/**
 * Role Test Page
 * Debug and test role validation issues
 */

import React, { useState, useEffect } from 'react';
import { useAuth } from '../../hooks/useAuth';
import { useRoles } from '../../hooks/useRoles';
import { RoleDebugger } from '../../utils/roleDebugger';

const RoleTestPage: React.FC = () => {
  const { isAuthenticated, user } = useAuth();
  const { primaryRole, userRoles, isLoading } = useRoles();
  const [debugInfo, setDebugInfo] = useState<any>(null);
  const [testRole, setTestRole] = useState('invalid-role');
  const [testResults, setTestResults] = useState<any[]>([]);

  useEffect(() => {
    const loadDebugInfo = async () => {
      const info = await RoleDebugger.getDebugInfo();
      setDebugInfo(info);
    };
    loadDebugInfo();
  }, []);

  const testRoleValidation = async (roleName: string) => {
    console.log(`🧪 Testing role: ${roleName}`);
    const result = await RoleDebugger.testRoleValidation(roleName);
    const newResult = {
      role: roleName,
      hasAccess: result,
      timestamp: new Date().toLocaleTimeString()
    };
    setTestResults(prev => [newResult, ...prev.slice(0, 9)]); // Keep last 10 results
  };

  const testInvalidRole = async (roleName: string) => {
    console.log(`🧪 Testing invalid role: ${roleName}`);
    await RoleDebugger.testInvalidRoleHandling(roleName);
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        <div className="bg-white rounded-lg shadow-lg p-6">
          <h1 className="text-3xl font-bold text-gray-900 mb-6">Role Testing & Debug Page</h1>
          
          {/* Authentication Status */}
          <div className="mb-6 p-4 bg-blue-50 rounded-lg">
            <h2 className="text-xl font-semibold mb-3">Authentication Status</h2>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <span className="font-medium">Authenticated:</span> 
                <span className={`ml-2 px-2 py-1 rounded text-sm ${isAuthenticated ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                  {isAuthenticated ? 'Yes' : 'No'}
                </span>
              </div>
              <div>
                <span className="font-medium">Loading:</span> 
                <span className={`ml-2 px-2 py-1 rounded text-sm ${isLoading ? 'bg-yellow-100 text-yellow-800' : 'bg-green-100 text-green-800'}`}>
                  {isLoading ? 'Yes' : 'No'}
                </span>
              </div>
              <div>
                <span className="font-medium">Primary Role:</span> 
                <span className="ml-2 font-mono bg-gray-100 px-2 py-1 rounded">{primaryRole || 'None'}</span>
              </div>
              <div>
                <span className="font-medium">User ID:</span> 
                <span className="ml-2 font-mono bg-gray-100 px-2 py-1 rounded">{user?.id || 'None'}</span>
              </div>
            </div>
          </div>

          {/* Debug Information */}
          {debugInfo && (
            <div className="mb-6 p-4 bg-gray-50 rounded-lg">
              <h2 className="text-xl font-semibold mb-3">Debug Information</h2>
              <pre className="bg-gray-800 text-green-400 p-4 rounded text-sm overflow-auto">
                {JSON.stringify(debugInfo, null, 2)}
              </pre>
            </div>
          )}

          {/* Role Testing */}
          <div className="mb-6 p-4 bg-yellow-50 rounded-lg">
            <h2 className="text-xl font-semibold mb-3">Role Testing</h2>
            <div className="flex gap-4 mb-4">
              <input
                type="text"
                value={testRole}
                onChange={(e) => setTestRole(e.target.value)}
                placeholder="Enter role name to test"
                className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
              <button
                onClick={() => testRoleValidation(testRole)}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
              >
                Test Role
              </button>
              <button
                onClick={() => testInvalidRole(testRole)}
                className="px-4 py-2 bg-orange-600 text-white rounded-md hover:bg-orange-700"
              >
                Test Invalid
              </button>
            </div>

            {/* Quick Test Buttons */}
            <div className="flex flex-wrap gap-2 mb-4">
              {['user', 'admin', 'super_admin', 'invalid-role', 'badRole', 'entrepreneur'].map(role => (
                <button
                  key={role}
                  onClick={() => testRoleValidation(role)}
                  className="px-3 py-1 bg-gray-200 text-gray-700 rounded hover:bg-gray-300 text-sm"
                >
                  {role}
                </button>
              ))}
            </div>

            {/* Test Results */}
            {testResults.length > 0 && (
              <div>
                <h3 className="font-semibold mb-2">Test Results:</h3>
                <div className="space-y-2">
                  {testResults.map((result, index) => (
                    <div key={index} className="flex items-center gap-4 p-2 bg-white rounded border">
                      <span className="font-mono text-sm">{result.timestamp}</span>
                      <span className="font-medium">{result.role}</span>
                      <span className={`px-2 py-1 rounded text-sm ${result.hasAccess ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                        {result.hasAccess ? 'Access Granted' : 'Access Denied'}
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* Navigation Test Links */}
          <div className="mb-6 p-4 bg-purple-50 rounded-lg">
            <h2 className="text-xl font-semibold mb-3">Navigation Test Links</h2>
            <div className="grid grid-cols-2 gap-2">
              <a href="/user/dashboard" className="text-blue-600 hover:underline">/user/dashboard</a>
              <a href="/admin/dashboard" className="text-blue-600 hover:underline">/admin/dashboard</a>
              <a href="/invalid-role/dashboard" className="text-red-600 hover:underline">/invalid-role/dashboard</a>
              <a href="/badRole/profile" className="text-red-600 hover:underline">/badRole/profile</a>
              <a href="/super_admin/settings" className="text-blue-600 hover:underline">/super_admin/settings</a>
              <a href="/entrepreneur/business-plans" className="text-blue-600 hover:underline">/entrepreneur/business-plans</a>
            </div>
          </div>

          {/* Console Instructions */}
          <div className="p-4 bg-green-50 rounded-lg">
            <h2 className="text-xl font-semibold mb-3">Console Debugging</h2>
            <p className="text-gray-700 mb-2">Open browser console and use these commands:</p>
            <div className="bg-gray-800 text-green-400 p-3 rounded font-mono text-sm">
              <div>RoleDebugger.getDebugInfo()</div>
              <div>RoleDebugger.testRoleValidation('invalid-role')</div>
              <div>RoleDebugger.testInvalidRoleHandling('badRole')</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RoleTestPage;
