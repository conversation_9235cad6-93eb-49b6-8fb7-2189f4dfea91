/**
 * Investor Portfolio Page
 * Comprehensive portfolio management and analytics
 */

import React, { useState, useEffect } from 'react';
import { useLanguage } from '../../hooks/useLanguage';
import { ArabicCard, ArabicButton, ArabicTypography } from '../../components/ui/ArabicOptimizedComponents';
import { 
  Briefcase, 
  TrendingUp, 
  TrendingDown,
  DollarSign, 
  PieChart,
  BarChart3,
  Calendar,
  Eye,
  Download,
  Filter,
  RefreshCw,
  Target,
  Award,
  AlertTriangle
} from 'lucide-react';

interface PortfolioCompany {
  id: string;
  name: string;
  industry: string;
  stage: string;
  investmentAmount: number;
  currentValuation: number;
  equity: number;
  investmentDate: string;
  lastUpdate: string;
  status: 'active' | 'exited' | 'failed' | 'ipo';
  performance: 'outperforming' | 'meeting' | 'underperforming';
  roi: number;
  logo?: string;
  description: string;
  founders: string[];
  location: string;
}

interface PortfolioMetrics {
  totalInvested: number;
  currentValue: number;
  totalROI: number;
  unrealizedGains: number;
  realizedGains: number;
  numberOfInvestments: number;
  averageInvestment: number;
  topPerformer: string;
  portfolioDiversification: {
    byStage: Record<string, number>;
    byIndustry: Record<string, number>;
    byGeography: Record<string, number>;
  };
}

const InvestorPortfolioPage: React.FC = () => {
  const { language, isRTL } = useLanguage();
  const [companies, setCompanies] = useState<PortfolioCompany[]>([]);
  const [metrics, setMetrics] = useState<PortfolioMetrics | null>(null);
  const [loading, setLoading] = useState(true);
  const [selectedFilter, setSelectedFilter] = useState('all');
  const [viewMode, setViewMode] = useState<'grid' | 'table'>('grid');

  useEffect(() => {
    loadPortfolioData();
  }, [selectedFilter]);

  const loadPortfolioData = async () => {
    setLoading(true);
    try {
      // TODO: Replace with real API call
      const mockCompanies: PortfolioCompany[] = [
        {
          id: '1',
          name: 'TechStart Syria',
          industry: 'Technology',
          stage: 'Seed',
          investmentAmount: 50000,
          currentValuation: 750000,
          equity: 8,
          investmentDate: '2023-06-15',
          lastUpdate: '2024-01-15',
          status: 'active',
          performance: 'outperforming',
          roi: 50,
          description: 'AI-powered e-commerce platform',
          founders: ['Ahmad Al-Rashid', 'Layla Hassan'],
          location: 'Damascus, Syria'
        },
        {
          id: '2',
          name: 'GreenTech Solutions',
          industry: 'Clean Energy',
          stage: 'Series A',
          investmentAmount: 100000,
          currentValuation: 3000000,
          equity: 5,
          investmentDate: '2022-03-20',
          lastUpdate: '2024-01-10',
          status: 'active',
          performance: 'meeting',
          roi: 200,
          description: 'Solar energy solutions',
          founders: ['Omar Khalil'],
          location: 'Aleppo, Syria'
        }
      ];

      const mockMetrics: PortfolioMetrics = {
        totalInvested: 750000,
        currentValue: 1125000,
        totalROI: 50,
        unrealizedGains: 375000,
        realizedGains: 0,
        numberOfInvestments: 12,
        averageInvestment: 62500,
        topPerformer: 'GreenTech Solutions',
        portfolioDiversification: {
          byStage: { 'Seed': 40, 'Series A': 35, 'Series B': 25 },
          byIndustry: { 'Technology': 45, 'Healthcare': 25, 'Clean Energy': 20, 'Fintech': 10 },
          byGeography: { 'Syria': 60, 'Jordan': 25, 'Lebanon': 15 }
        }
      };

      setCompanies(mockCompanies);
      setMetrics(mockMetrics);
    } catch (error) {
      console.error('Error loading portfolio data:', error);
    } finally {
      setLoading(false);
    }
  };

  const getPerformanceColor = (performance: string) => {
    switch (performance) {
      case 'outperforming': return 'text-green-600 bg-green-100';
      case 'meeting': return 'text-blue-600 bg-blue-100';
      case 'underperforming': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getPerformanceIcon = (performance: string) => {
    switch (performance) {
      case 'outperforming': return <TrendingUp className="w-4 h-4" />;
      case 'meeting': return <Target className="w-4 h-4" />;
      case 'underperforming': return <TrendingDown className="w-4 h-4" />;
      default: return <BarChart3 className="w-4 h-4" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800';
      case 'exited': return 'bg-blue-100 text-blue-800';
      case 'failed': return 'bg-red-100 text-red-800';
      case 'ipo': return 'bg-purple-100 text-purple-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const formatPercentage = (value: number) => {
    return `${value > 0 ? '+' : ''}${value.toFixed(1)}%`;
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div className={`p-6 space-y-6 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* Header */}
      <div className={`flex justify-between items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
        <div>
          <ArabicTypography variant="h1" className="text-3xl font-bold text-gray-900">
            {language === 'ar' ? 'محفظة الاستثمارات' : 'Investment Portfolio'}
          </ArabicTypography>
          <ArabicTypography variant="body1" className="text-gray-600 mt-1">
            {language === 'ar' ? 'نظرة شاملة على استثماراتك وأدائها' : 'Comprehensive view of your investments and performance'}
          </ArabicTypography>
        </div>
        <div className={`flex gap-2 ${isRTL ? 'flex-row-reverse' : ''}`}>
          <ArabicButton variant="outline" icon={<RefreshCw className="w-4 h-4" />} onClick={loadPortfolioData}>
            {language === 'ar' ? 'تحديث' : 'Refresh'}
          </ArabicButton>
          <ArabicButton variant="outline" icon={<Download className="w-4 h-4" />}>
            {language === 'ar' ? 'تصدير' : 'Export'}
          </ArabicButton>
          <ArabicButton variant="outline" icon={<Filter className="w-4 h-4" />}>
            {language === 'ar' ? 'تصفية' : 'Filter'}
          </ArabicButton>
        </div>
      </div>

      {/* Portfolio Overview */}
      {metrics && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <ArabicCard>
            <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
              <div className="p-3 bg-blue-100 rounded-lg">
                <Briefcase className="w-6 h-6 text-blue-600" />
              </div>
              <div className={`${isRTL ? 'mr-4' : 'ml-4'}`}>
                <ArabicTypography variant="body2" className="text-gray-600">
                  {language === 'ar' ? 'القيمة الحالية' : 'Current Value'}
                </ArabicTypography>
                <ArabicTypography variant="h3" className="text-2xl font-bold">
                  {formatCurrency(metrics.currentValue)}
                </ArabicTypography>
              </div>
            </div>
          </ArabicCard>

          <ArabicCard>
            <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
              <div className="p-3 bg-green-100 rounded-lg">
                <TrendingUp className="w-6 h-6 text-green-600" />
              </div>
              <div className={`${isRTL ? 'mr-4' : 'ml-4'}`}>
                <ArabicTypography variant="body2" className="text-gray-600">
                  {language === 'ar' ? 'إجمالي العائد' : 'Total ROI'}
                </ArabicTypography>
                <ArabicTypography variant="h3" className="text-2xl font-bold text-green-600">
                  {formatPercentage(metrics.totalROI)}
                </ArabicTypography>
              </div>
            </div>
          </ArabicCard>

          <ArabicCard>
            <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
              <div className="p-3 bg-purple-100 rounded-lg">
                <PieChart className="w-6 h-6 text-purple-600" />
              </div>
              <div className={`${isRTL ? 'mr-4' : 'ml-4'}`}>
                <ArabicTypography variant="body2" className="text-gray-600">
                  {language === 'ar' ? 'عدد الاستثمارات' : 'Investments'}
                </ArabicTypography>
                <ArabicTypography variant="h3" className="text-2xl font-bold">
                  {metrics.numberOfInvestments}
                </ArabicTypography>
              </div>
            </div>
          </ArabicCard>

          <ArabicCard>
            <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
              <div className="p-3 bg-orange-100 rounded-lg">
                <Award className="w-6 h-6 text-orange-600" />
              </div>
              <div className={`${isRTL ? 'mr-4' : 'ml-4'}`}>
                <ArabicTypography variant="body2" className="text-gray-600">
                  {language === 'ar' ? 'الأفضل أداءً' : 'Top Performer'}
                </ArabicTypography>
                <ArabicTypography variant="body1" className="font-semibold text-sm">
                  {metrics.topPerformer}
                </ArabicTypography>
              </div>
            </div>
          </ArabicCard>
        </div>
      )}

      {/* View Mode Toggle */}
      <div className={`flex gap-2 ${isRTL ? 'flex-row-reverse' : ''}`}>
        <ArabicButton
          variant={viewMode === 'grid' ? 'default' : 'outline'}
          onClick={() => setViewMode('grid')}
        >
          {language === 'ar' ? 'شبكة' : 'Grid'}
        </ArabicButton>
        <ArabicButton
          variant={viewMode === 'table' ? 'default' : 'outline'}
          onClick={() => setViewMode('table')}
        >
          {language === 'ar' ? 'جدول' : 'Table'}
        </ArabicButton>
      </div>

      {/* Portfolio Companies */}
      {viewMode === 'grid' ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {companies.map((company) => (
            <ArabicCard key={company.id}>
              <div className="p-6">
                <div className={`flex justify-between items-start mb-4 ${isRTL ? 'flex-row-reverse' : ''}`}>
                  <div className="flex-1">
                    <ArabicTypography variant="h3" className="text-lg font-semibold mb-1">
                      {company.name}
                    </ArabicTypography>
                    <ArabicTypography variant="body2" className="text-gray-600">
                      {company.industry} • {company.stage}
                    </ArabicTypography>
                    <ArabicTypography variant="body2" className="text-gray-500 text-sm">
                      {company.location}
                    </ArabicTypography>
                  </div>
                  <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(company.status)}`}>
                    {language === 'ar' 
                      ? (company.status === 'active' ? 'نشط' : 
                         company.status === 'exited' ? 'خرج' : 
                         company.status === 'failed' ? 'فشل' : 'طرح عام')
                      : company.status
                    }
                  </span>
                </div>

                <div className="space-y-3">
                  <div className={`flex justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                    <ArabicTypography variant="body2" className="text-gray-600">
                      {language === 'ar' ? 'الاستثمار:' : 'Investment:'}
                    </ArabicTypography>
                    <ArabicTypography variant="body2" className="font-semibold">
                      {formatCurrency(company.investmentAmount)}
                    </ArabicTypography>
                  </div>
                  <div className={`flex justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                    <ArabicTypography variant="body2" className="text-gray-600">
                      {language === 'ar' ? 'الحصة:' : 'Equity:'}
                    </ArabicTypography>
                    <ArabicTypography variant="body2" className="font-semibold">
                      {company.equity}%
                    </ArabicTypography>
                  </div>
                  <div className={`flex justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                    <ArabicTypography variant="body2" className="text-gray-600">
                      {language === 'ar' ? 'العائد:' : 'ROI:'}
                    </ArabicTypography>
                    <div className={`flex items-center gap-1 ${isRTL ? 'flex-row-reverse' : ''}`}>
                      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getPerformanceColor(company.performance)}`}>
                        {getPerformanceIcon(company.performance)}
                        <span className={`${isRTL ? 'mr-1' : 'ml-1'}`}>
                          {formatPercentage(company.roi)}
                        </span>
                      </span>
                    </div>
                  </div>
                </div>

                <div className={`flex gap-2 mt-4 ${isRTL ? 'flex-row-reverse' : ''}`}>
                  <ArabicButton size="sm" variant="outline" icon={<Eye className="w-3 h-3" />}>
                    {language === 'ar' ? 'عرض' : 'View'}
                  </ArabicButton>
                  <ArabicButton size="sm" variant="outline" icon={<BarChart3 className="w-3 h-3" />}>
                    {language === 'ar' ? 'تحليلات' : 'Analytics'}
                  </ArabicButton>
                </div>
              </div>
            </ArabicCard>
          ))}
        </div>
      ) : (
        <ArabicCard>
          <div className="p-6">
            <ArabicTypography variant="h2" className="text-xl font-semibold mb-4">
              {language === 'ar' ? 'تفاصيل المحفظة' : 'Portfolio Details'}
            </ArabicTypography>
            
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b">
                    <th className={`py-3 px-4 ${isRTL ? 'text-right' : 'text-left'}`}>
                      {language === 'ar' ? 'الشركة' : 'Company'}
                    </th>
                    <th className={`py-3 px-4 ${isRTL ? 'text-right' : 'text-left'}`}>
                      {language === 'ar' ? 'المرحلة' : 'Stage'}
                    </th>
                    <th className={`py-3 px-4 ${isRTL ? 'text-right' : 'text-left'}`}>
                      {language === 'ar' ? 'الاستثمار' : 'Investment'}
                    </th>
                    <th className={`py-3 px-4 ${isRTL ? 'text-right' : 'text-left'}`}>
                      {language === 'ar' ? 'الحصة' : 'Equity'}
                    </th>
                    <th className={`py-3 px-4 ${isRTL ? 'text-right' : 'text-left'}`}>
                      {language === 'ar' ? 'العائد' : 'ROI'}
                    </th>
                    <th className={`py-3 px-4 ${isRTL ? 'text-right' : 'text-left'}`}>
                      {language === 'ar' ? 'الحالة' : 'Status'}
                    </th>
                    <th className={`py-3 px-4 ${isRTL ? 'text-right' : 'text-left'}`}>
                      {language === 'ar' ? 'إجراءات' : 'Actions'}
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {companies.map((company) => (
                    <tr key={company.id} className="border-b hover:bg-gray-50">
                      <td className="py-3 px-4">
                        <div>
                          <ArabicTypography variant="body1" className="font-medium">
                            {company.name}
                          </ArabicTypography>
                          <ArabicTypography variant="body2" className="text-gray-600">
                            {company.industry}
                          </ArabicTypography>
                        </div>
                      </td>
                      <td className="py-3 px-4">
                        <ArabicTypography variant="body2">
                          {company.stage}
                        </ArabicTypography>
                      </td>
                      <td className="py-3 px-4">
                        <ArabicTypography variant="body1" className="font-semibold">
                          {formatCurrency(company.investmentAmount)}
                        </ArabicTypography>
                      </td>
                      <td className="py-3 px-4">
                        <ArabicTypography variant="body1">
                          {company.equity}%
                        </ArabicTypography>
                      </td>
                      <td className="py-3 px-4">
                        <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getPerformanceColor(company.performance)}`}>
                          {getPerformanceIcon(company.performance)}
                          <span className={`${isRTL ? 'mr-1' : 'ml-1'}`}>
                            {formatPercentage(company.roi)}
                          </span>
                        </span>
                      </td>
                      <td className="py-3 px-4">
                        <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(company.status)}`}>
                          {language === 'ar' 
                            ? (company.status === 'active' ? 'نشط' : 
                               company.status === 'exited' ? 'خرج' : 
                               company.status === 'failed' ? 'فشل' : 'طرح عام')
                            : company.status
                          }
                        </span>
                      </td>
                      <td className="py-3 px-4">
                        <div className={`flex gap-1 ${isRTL ? 'flex-row-reverse' : ''}`}>
                          <ArabicButton size="sm" variant="outline" icon={<Eye className="w-3 h-3" />}>
                            {language === 'ar' ? 'عرض' : 'View'}
                          </ArabicButton>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </ArabicCard>
      )}
    </div>
  );
};

export default InvestorPortfolioPage;
