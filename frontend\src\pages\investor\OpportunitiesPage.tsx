import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import {
  Search,
  TrendingUp,
  DollarSign,
  Users,
  Calendar,
  Star,
  ArrowRight,
  Filter,
  MapPin,
  Briefcase,
  Target,
  Eye,
  Heart,
  BookmarkPlus
} from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../hooks/useLanguage';
import AuthenticatedLayout from '../../components/layout/AuthenticatedLayout';

interface InvestmentOpportunity {
  id: string;
  companyName: string;
  businessIdea: string;
  description: string;
  industry: string;
  stage: 'seed' | 'series-a' | 'series-b' | 'growth';
  fundingGoal: number;
  currentFunding: number;
  valuation: number;
  location: string;
  foundedDate: string;
  teamSize: number;
  revenue?: number;
  growth?: number;
  founderName: string;
  founderAvatar?: string;
  tags: string[];
  featured: boolean;
  saved: boolean;
  views: number;
  investors: number;
}

const OpportunitiesPage: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const navigate = useNavigate();

  const [opportunities, setOpportunities] = useState<InvestmentOpportunity[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [industryFilter, setIndustryFilter] = useState<string>('all');
  const [stageFilter, setStageFilter] = useState<string>('all');
  const [sortBy, setSortBy] = useState<string>('featured');

  // Mock data - replace with actual API call
  useEffect(() => {
    const fetchOpportunities = async () => {
      setIsLoading(true);
      try {
        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        const mockOpportunities: InvestmentOpportunity[] = [
          {
            id: '1',
            companyName: 'تقنيات الذكاء الاصطناعي',
            businessIdea: 'منصة ذكية لتحليل البيانات',
            description: 'منصة متقدمة تستخدم الذكاء الاصطناعي لتحليل البيانات التجارية وتقديم رؤى قابلة للتنفيذ',
            industry: 'Technology',
            stage: 'series-a',
            fundingGoal: 2000000,
            currentFunding: 800000,
            valuation: 10000000,
            location: 'الرياض، السعودية',
            foundedDate: '2023-01-15',
            teamSize: 12,
            revenue: 500000,
            growth: 150,
            founderName: 'أحمد الخالد',
            tags: ['AI', 'Analytics', 'SaaS', 'B2B'],
            featured: true,
            saved: false,
            views: 1250,
            investors: 8
          },
          {
            id: '2',
            companyName: 'صحتي الذكية',
            businessIdea: 'تطبيق الرعاية الصحية الرقمية',
            description: 'تطبيق شامل للرعاية الصحية يربط المرضى بالأطباء ويوفر خدمات طبية متقدمة',
            industry: 'Healthcare',
            stage: 'seed',
            fundingGoal: 500000,
            currentFunding: 150000,
            valuation: 3000000,
            location: 'دبي، الإمارات',
            foundedDate: '2023-06-01',
            teamSize: 6,
            revenue: 50000,
            growth: 200,
            founderName: 'فاطمة النعيمي',
            tags: ['Healthcare', 'Mobile', 'Telemedicine'],
            featured: false,
            saved: true,
            views: 890,
            investors: 4
          },
          {
            id: '3',
            companyName: 'التجارة الخضراء',
            businessIdea: 'منصة التجارة الإلكترونية المستدامة',
            description: 'منصة تجارة إلكترونية تركز على المنتجات الصديقة للبيئة والاستدامة',
            industry: 'E-commerce',
            stage: 'series-b',
            fundingGoal: 5000000,
            currentFunding: 3200000,
            valuation: 25000000,
            location: 'القاهرة، مصر',
            foundedDate: '2022-03-10',
            teamSize: 25,
            revenue: 2000000,
            growth: 120,
            founderName: 'محمد حسن',
            tags: ['E-commerce', 'Sustainability', 'Green Tech'],
            featured: true,
            saved: false,
            views: 2100,
            investors: 15
          }
        ];
        
        setOpportunities(mockOpportunities);
      } catch (error) {
        console.error('Error fetching opportunities:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchOpportunities();
  }, []);

  const filteredOpportunities = opportunities
    .filter(opp => {
      const matchesSearch = opp.companyName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           opp.businessIdea.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           opp.description.toLowerCase().includes(searchTerm.toLowerCase());
      const matchesIndustry = industryFilter === 'all' || opp.industry === industryFilter;
      const matchesStage = stageFilter === 'all' || opp.stage === stageFilter;
      return matchesSearch && matchesIndustry && matchesStage;
    })
    .sort((a, b) => {
      switch (sortBy) {
        case 'featured':
          return b.featured ? 1 : -1;
        case 'funding':
          return b.currentFunding - a.currentFunding;
        case 'growth':
          return (b.growth || 0) - (a.growth || 0);
        case 'views':
          return b.views - a.views;
        default:
          return 0;
      }
    });

  const getStageColor = (stage: string) => {
    switch (stage) {
      case 'seed': return 'text-green-400 bg-green-400/10';
      case 'series-a': return 'text-blue-400 bg-blue-400/10';
      case 'series-b': return 'text-purple-400 bg-purple-400/10';
      case 'growth': return 'text-orange-400 bg-orange-400/10';
      default: return 'text-gray-400 bg-gray-400/10';
    }
  };

  const formatCurrency = (amount: number) => {
    if (amount >= 1000000) {
      return `$${(amount / 1000000).toFixed(1)}M`;
    } else if (amount >= 1000) {
      return `$${(amount / 1000).toFixed(0)}K`;
    }
    return `$${amount}`;
  };

  const getFundingProgress = (current: number, goal: number) => {
    return Math.min((current / goal) * 100, 100);
  };

  return (
    <AuthenticatedLayout>
      <div className="min-h-screen bg-gradient-to-br from-indigo-900 via-purple-900 to-pink-900 p-6">
        <div className="max-w-7xl mx-auto">
          {/* Header */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-white mb-2">
              {t('investor.opportunities.title', 'Investment Opportunities')}
            </h1>
            <p className="text-gray-300">
              {t('investor.opportunities.subtitle', 'Discover promising startups and investment opportunities')}
            </p>
          </div>

          {/* Filters */}
          <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 mb-6 border border-white/20">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div className="relative">
                <Search size={20} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <input
                  type="text"
                  placeholder={t('investor.opportunities.search', 'Search opportunities...')}
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 bg-white/10 border border-white/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-indigo-500"
                />
              </div>
              <select
                value={industryFilter}
                onChange={(e) => setIndustryFilter(e.target.value)}
                className="px-3 py-2 bg-white/10 border border-white/20 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-indigo-500"
              >
                <option value="all">{t('common.allIndustries', 'All Industries')}</option>
                <option value="Technology">{t('industry.technology', 'Technology')}</option>
                <option value="Healthcare">{t('industry.healthcare', 'Healthcare')}</option>
                <option value="E-commerce">{t('industry.ecommerce', 'E-commerce')}</option>
                <option value="Fintech">{t('industry.fintech', 'Fintech')}</option>
              </select>
              <select
                value={stageFilter}
                onChange={(e) => setStageFilter(e.target.value)}
                className="px-3 py-2 bg-white/10 border border-white/20 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-indigo-500"
              >
                <option value="all">{t('common.allStages', 'All Stages')}</option>
                <option value="seed">{t('stage.seed', 'Seed')}</option>
                <option value="series-a">{t('stage.seriesA', 'Series A')}</option>
                <option value="series-b">{t('stage.seriesB', 'Series B')}</option>
                <option value="growth">{t('stage.growth', 'Growth')}</option>
              </select>
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value)}
                className="px-3 py-2 bg-white/10 border border-white/20 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-indigo-500"
              >
                <option value="featured">{t('sort.featured', 'Featured')}</option>
                <option value="funding">{t('sort.funding', 'Funding Amount')}</option>
                <option value="growth">{t('sort.growth', 'Growth Rate')}</option>
                <option value="views">{t('sort.views', 'Most Viewed')}</option>
              </select>
            </div>
          </div>

          {/* Stats */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-gray-300 text-sm">{t('investor.stats.totalOpportunities', 'Total Opportunities')}</p>
                  <p className="text-2xl font-bold text-white">{opportunities.length}</p>
                </div>
                <Briefcase className="text-indigo-400" size={24} />
              </div>
            </div>
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-gray-300 text-sm">{t('investor.stats.totalFunding', 'Total Funding')}</p>
                  <p className="text-2xl font-bold text-white">
                    {formatCurrency(opportunities.reduce((sum, opp) => sum + opp.currentFunding, 0))}
                  </p>
                </div>
                <DollarSign className="text-green-400" size={24} />
              </div>
            </div>
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-gray-300 text-sm">{t('investor.stats.avgGrowth', 'Avg Growth')}</p>
                  <p className="text-2xl font-bold text-white">
                    {Math.round(opportunities.reduce((sum, opp) => sum + (opp.growth || 0), 0) / opportunities.length)}%
                  </p>
                </div>
                <TrendingUp className="text-purple-400" size={24} />
              </div>
            </div>
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-gray-300 text-sm">{t('investor.stats.saved', 'Saved')}</p>
                  <p className="text-2xl font-bold text-white">
                    {opportunities.filter(opp => opp.saved).length}
                  </p>
                </div>
                <Heart className="text-pink-400" size={24} />
              </div>
            </div>
          </div>

          {/* Opportunities List */}
          <div className="space-y-6">
            {isLoading ? (
              <div className="bg-white/10 backdrop-blur-sm rounded-lg p-8 border border-white/20 text-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white mx-auto"></div>
                <p className="text-gray-300 mt-2">{t('common.loading', 'Loading...')}</p>
              </div>
            ) : filteredOpportunities.length === 0 ? (
              <div className="bg-white/10 backdrop-blur-sm rounded-lg p-8 border border-white/20 text-center">
                <Search size={48} className="mx-auto text-gray-500 mb-4" />
                <h3 className="text-lg font-medium text-white mb-2">
                  {t('investor.opportunities.noOpportunities', 'No opportunities found')}
                </h3>
                <p className="text-gray-400">
                  {t('investor.opportunities.noOpportunitiesDesc', 'Try adjusting your search criteria')}
                </p>
              </div>
            ) : (
              filteredOpportunities.map((opportunity) => (
                <div key={opportunity.id} className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20 hover:bg-white/15 transition-colors">
                  <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
                    <div className="flex-1">
                      <div className="flex items-center space-x-3 mb-3">
                        <h3 className="text-xl font-bold text-white">{opportunity.companyName}</h3>
                        {opportunity.featured && (
                          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium text-yellow-400 bg-yellow-400/10">
                            <Star size={12} className="mr-1" />
                            {t('common.featured', 'Featured')}
                          </span>
                        )}
                        <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStageColor(opportunity.stage)}`}>
                          {t(`stage.${opportunity.stage}`, opportunity.stage)}
                        </span>
                      </div>
                      <p className="text-lg text-indigo-300 mb-2">{opportunity.businessIdea}</p>
                      <p className="text-gray-300 mb-4">{opportunity.description}</p>
                      
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                        <div>
                          <p className="text-gray-400 text-sm">{t('investor.funding.goal', 'Funding Goal')}</p>
                          <p className="text-white font-semibold">{formatCurrency(opportunity.fundingGoal)}</p>
                        </div>
                        <div>
                          <p className="text-gray-400 text-sm">{t('investor.funding.raised', 'Raised')}</p>
                          <p className="text-white font-semibold">{formatCurrency(opportunity.currentFunding)}</p>
                        </div>
                        <div>
                          <p className="text-gray-400 text-sm">{t('investor.valuation', 'Valuation')}</p>
                          <p className="text-white font-semibold">{formatCurrency(opportunity.valuation)}</p>
                        </div>
                        <div>
                          <p className="text-gray-400 text-sm">{t('investor.growth', 'Growth')}</p>
                          <p className="text-white font-semibold">{opportunity.growth}%</p>
                        </div>
                      </div>

                      <div className="mb-4">
                        <div className="flex justify-between text-sm text-gray-300 mb-1">
                          <span>{t('investor.funding.progress', 'Funding Progress')}</span>
                          <span>{getFundingProgress(opportunity.currentFunding, opportunity.fundingGoal).toFixed(0)}%</span>
                        </div>
                        <div className="w-full bg-gray-700 rounded-full h-2">
                          <div 
                            className="bg-gradient-to-r from-indigo-500 to-purple-500 h-2 rounded-full"
                            style={{ width: `${getFundingProgress(opportunity.currentFunding, opportunity.fundingGoal)}%` }}
                          ></div>
                        </div>
                      </div>

                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-4 text-sm text-gray-400">
                          <span className="flex items-center">
                            <MapPin size={16} className="mr-1" />
                            {opportunity.location}
                          </span>
                          <span className="flex items-center">
                            <Users size={16} className="mr-1" />
                            {opportunity.teamSize} {t('common.team', 'team')}
                          </span>
                          <span className="flex items-center">
                            <Eye size={16} className="mr-1" />
                            {opportunity.views} {t('common.views', 'views')}
                          </span>
                        </div>
                        <div className="flex space-x-2">
                          <button className="p-2 text-gray-400 hover:text-white transition-colors">
                            <BookmarkPlus size={20} />
                          </button>
                          <button className="p-2 text-gray-400 hover:text-pink-400 transition-colors">
                            <Heart size={20} className={opportunity.saved ? 'fill-current text-pink-400' : ''} />
                          </button>
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex flex-col space-y-3">
                      <Link
                        to={`/investor/opportunities/${opportunity.id}`}
                        className="inline-flex items-center justify-center px-4 py-2 bg-indigo-600 hover:bg-indigo-700 text-white rounded-lg transition-colors"
                      >
                        {t('common.viewDetails', 'View Details')}
                        <ArrowRight size={16} className={`${isRTL ? 'mr-2' : 'ml-2'}`} />
                      </Link>
                      <Link
                        to={`/investor/opportunities/${opportunity.id}/invest`}
                        className="inline-flex items-center justify-center px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors"
                      >
                        <DollarSign size={16} className={`${isRTL ? 'ml-2' : 'mr-2'}`} />
                        {t('investor.invest', 'Invest')}
                      </Link>
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>
        </div>
      </div>
    </AuthenticatedLayout>
  );
};

export default OpportunitiesPage;
