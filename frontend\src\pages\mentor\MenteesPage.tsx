import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import {
  Users,
  MessageSquare,
  Calendar,
  TrendingUp,
  Star,
  Clock,
  ArrowRight,
  Plus,
  Search,
  Filter,
  MoreVertical,
  CheckCircle,
  AlertCircle
} from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../hooks/useLanguage';
import AuthenticatedLayout from '../../components/layout/AuthenticatedLayout';

interface Mentee {
  id: string;
  name: string;
  email: string;
  avatar?: string;
  businessIdea: string;
  progress: number;
  status: 'active' | 'inactive' | 'completed';
  joinDate: string;
  lastActivity: string;
  sessionsCompleted: number;
  nextSession?: string;
  goals: string[];
}

const MenteesPage: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const navigate = useNavigate();

  const [mentees, setMentees] = useState<Mentee[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');

  // Mock data - replace with actual API call
  useEffect(() => {
    const fetchMentees = async () => {
      setIsLoading(true);
      try {
        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        const mockMentees: Mentee[] = [
          {
            id: '1',
            name: 'أحمد محمد',
            email: '<EMAIL>',
            businessIdea: 'منصة التجارة الإلكترونية',
            progress: 75,
            status: 'active',
            joinDate: '2024-01-15',
            lastActivity: '2024-01-20',
            sessionsCompleted: 8,
            nextSession: '2024-01-25T10:00:00Z',
            goals: ['تطوير نموذج العمل', 'إيجاد التمويل', 'بناء الفريق']
          },
          {
            id: '2',
            name: 'فاطمة علي',
            email: '<EMAIL>',
            businessIdea: 'تطبيق الصحة الذكي',
            progress: 45,
            status: 'active',
            joinDate: '2024-02-01',
            lastActivity: '2024-01-18',
            sessionsCompleted: 4,
            nextSession: '2024-01-26T14:00:00Z',
            goals: ['دراسة السوق', 'تطوير المنتج الأولي']
          }
        ];
        
        setMentees(mockMentees);
      } catch (error) {
        console.error('Error fetching mentees:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchMentees();
  }, []);

  const filteredMentees = mentees.filter(mentee => {
    const matchesSearch = mentee.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         mentee.businessIdea.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'all' || mentee.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'text-green-400 bg-green-400/10';
      case 'inactive': return 'text-yellow-400 bg-yellow-400/10';
      case 'completed': return 'text-blue-400 bg-blue-400/10';
      default: return 'text-gray-400 bg-gray-400/10';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active': return <CheckCircle size={16} />;
      case 'inactive': return <Clock size={16} />;
      case 'completed': return <Star size={16} />;
      default: return <AlertCircle size={16} />;
    }
  };

  return (
    <AuthenticatedLayout>
      <div className="min-h-screen bg-gradient-to-br from-indigo-900 via-purple-900 to-pink-900 p-6">
        <div className="max-w-7xl mx-auto">
          {/* Header */}
          <div className="mb-8">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
              <div>
                <h1 className="text-3xl font-bold text-white mb-2">
                  {t('mentor.mentees.title', 'My Mentees')}
                </h1>
                <p className="text-gray-300">
                  {t('mentor.mentees.subtitle', 'Manage and track your mentees\' progress')}
                </p>
              </div>
              <Link
                to="/mentor/mentees/invite"
                className="inline-flex items-center px-4 py-2 bg-indigo-600 hover:bg-indigo-700 text-white rounded-lg transition-colors"
              >
                <Plus size={20} className={`${isRTL ? 'ml-2' : 'mr-2'}`} />
                {t('mentor.mentees.invite', 'Invite Mentee')}
              </Link>
            </div>
          </div>

          {/* Filters */}
          <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 mb-6 border border-white/20">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search size={20} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                  <input
                    type="text"
                    placeholder={t('mentor.mentees.search', 'Search mentees...')}
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full pl-10 pr-4 py-2 bg-white/10 border border-white/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-indigo-500"
                  />
                </div>
              </div>
              <div className="flex items-center gap-2">
                <Filter size={20} className="text-gray-400" />
                <select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  className="px-3 py-2 bg-white/10 border border-white/20 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-indigo-500"
                >
                  <option value="all">{t('common.all', 'All')}</option>
                  <option value="active">{t('status.active', 'Active')}</option>
                  <option value="inactive">{t('status.inactive', 'Inactive')}</option>
                  <option value="completed">{t('status.completed', 'Completed')}</option>
                </select>
              </div>
            </div>
          </div>

          {/* Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-gray-300 text-sm">{t('mentor.stats.total', 'Total Mentees')}</p>
                  <p className="text-2xl font-bold text-white">{mentees.length}</p>
                </div>
                <Users className="text-indigo-400" size={24} />
              </div>
            </div>
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-gray-300 text-sm">{t('mentor.stats.active', 'Active')}</p>
                  <p className="text-2xl font-bold text-white">
                    {mentees.filter(m => m.status === 'active').length}
                  </p>
                </div>
                <CheckCircle className="text-green-400" size={24} />
              </div>
            </div>
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-gray-300 text-sm">{t('mentor.stats.sessions', 'Total Sessions')}</p>
                  <p className="text-2xl font-bold text-white">
                    {mentees.reduce((sum, m) => sum + m.sessionsCompleted, 0)}
                  </p>
                </div>
                <Calendar className="text-purple-400" size={24} />
              </div>
            </div>
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-gray-300 text-sm">{t('mentor.stats.avgProgress', 'Avg Progress')}</p>
                  <p className="text-2xl font-bold text-white">
                    {Math.round(mentees.reduce((sum, m) => sum + m.progress, 0) / mentees.length || 0)}%
                  </p>
                </div>
                <TrendingUp className="text-pink-400" size={24} />
              </div>
            </div>
          </div>

          {/* Mentees List */}
          <div className="bg-white/10 backdrop-blur-sm rounded-lg border border-white/20 overflow-hidden">
            {isLoading ? (
              <div className="p-8 text-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white mx-auto"></div>
                <p className="text-gray-300 mt-2">{t('common.loading', 'Loading...')}</p>
              </div>
            ) : filteredMentees.length === 0 ? (
              <div className="p-8 text-center">
                <Users size={48} className="mx-auto text-gray-500 mb-4" />
                <h3 className="text-lg font-medium text-white mb-2">
                  {t('mentor.mentees.noMentees', 'No mentees found')}
                </h3>
                <p className="text-gray-400">
                  {t('mentor.mentees.noMenteesDesc', 'Start by inviting your first mentee')}
                </p>
              </div>
            ) : (
              <div className="divide-y divide-white/10">
                {filteredMentees.map((mentee) => (
                  <div key={mentee.id} className="p-6 hover:bg-white/5 transition-colors">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-4">
                        <div className="w-12 h-12 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-full flex items-center justify-center">
                          <span className="text-white font-semibold">
                            {mentee.name.charAt(0)}
                          </span>
                        </div>
                        <div>
                          <h3 className="text-lg font-semibold text-white">{mentee.name}</h3>
                          <p className="text-gray-300">{mentee.businessIdea}</p>
                          <div className="flex items-center space-x-4 mt-2">
                            <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(mentee.status)}`}>
                              {getStatusIcon(mentee.status)}
                              <span className="ml-1">{t(`status.${mentee.status}`, mentee.status)}</span>
                            </span>
                            <span className="text-gray-400 text-sm">
                              {mentee.sessionsCompleted} {t('mentor.sessions', 'sessions')}
                            </span>
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center space-x-4">
                        <div className="text-right">
                          <div className="text-white font-semibold">{mentee.progress}%</div>
                          <div className="w-24 bg-gray-700 rounded-full h-2 mt-1">
                            <div 
                              className="bg-gradient-to-r from-indigo-500 to-purple-500 h-2 rounded-full"
                              style={{ width: `${mentee.progress}%` }}
                            ></div>
                          </div>
                        </div>
                        <Link
                          to={`/mentor/mentees/${mentee.id}`}
                          className="inline-flex items-center px-3 py-2 bg-indigo-600 hover:bg-indigo-700 text-white rounded-lg transition-colors"
                        >
                          {t('common.view', 'View')}
                          <ArrowRight size={16} className={`${isRTL ? 'mr-2' : 'ml-2'}`} />
                        </Link>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
    </AuthenticatedLayout>
  );
};

export default MenteesPage;
