/**
 * Mentor Calendar Page
 * Manage mentorship sessions and availability
 */

import React, { useState, useEffect } from 'react';
import { useLanguage } from '../../hooks/useLanguage';
import { ArabicCard, ArabicButton, ArabicTypography } from '../../components/ui/ArabicOptimizedComponents';
import { 
  Calendar, 
  Clock, 
  Plus,
  Edit,
  Trash2,
  Video,
  Phone,
  MapPin,
  User,
  ChevronLeft,
  ChevronRight,
  Filter,
  Download
} from 'lucide-react';

interface CalendarEvent {
  id: string;
  title: string;
  menteeName: string;
  date: string;
  startTime: string;
  endTime: string;
  type: 'video' | 'phone' | 'in-person';
  status: 'scheduled' | 'completed' | 'cancelled';
  location?: string;
  notes?: string;
  meetingLink?: string;
}

interface TimeSlot {
  id: string;
  date: string;
  startTime: string;
  endTime: string;
  isAvailable: boolean;
  isBooked: boolean;
  eventId?: string;
}

const MentorCalendarPage: React.FC = () => {
  const { language, isRTL } = useLanguage();
  const [currentDate, setCurrentDate] = useState(new Date());
  const [events, setEvents] = useState<CalendarEvent[]>([]);
  const [timeSlots, setTimeSlots] = useState<TimeSlot[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedDate, setSelectedDate] = useState<Date | null>(null);
  const [viewMode, setViewMode] = useState<'month' | 'week' | 'day'>('month');

  useEffect(() => {
    loadCalendarData();
  }, [currentDate]);

  const loadCalendarData = async () => {
    setLoading(true);
    try {
      // TODO: Replace with real API call
      const mockEvents: CalendarEvent[] = [
        {
          id: '1',
          title: 'Business Strategy Session',
          menteeName: 'Ahmed Hassan',
          date: '2024-01-15',
          startTime: '10:00',
          endTime: '11:00',
          type: 'video',
          status: 'scheduled',
          meetingLink: 'https://meet.google.com/abc-def-ghi'
        },
        {
          id: '2',
          title: 'Career Guidance',
          menteeName: 'Sara Mohamed',
          date: '2024-01-16',
          startTime: '14:00',
          endTime: '15:30',
          type: 'phone',
          status: 'scheduled'
        }
      ];

      const mockTimeSlots: TimeSlot[] = [
        {
          id: '1',
          date: '2024-01-15',
          startTime: '09:00',
          endTime: '10:00',
          isAvailable: true,
          isBooked: false
        },
        {
          id: '2',
          date: '2024-01-15',
          startTime: '10:00',
          endTime: '11:00',
          isAvailable: true,
          isBooked: true,
          eventId: '1'
        }
      ];

      setEvents(mockEvents);
      setTimeSlots(mockTimeSlots);
    } catch (error) {
      console.error('Error loading calendar data:', error);
    } finally {
      setLoading(false);
    }
  };

  const getEventTypeIcon = (type: string) => {
    switch (type) {
      case 'video': return <Video className="w-4 h-4" />;
      case 'phone': return <Phone className="w-4 h-4" />;
      case 'in-person': return <MapPin className="w-4 h-4" />;
      default: return <Calendar className="w-4 h-4" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'scheduled': return 'bg-blue-100 text-blue-800';
      case 'completed': return 'bg-green-100 text-green-800';
      case 'cancelled': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const navigateMonth = (direction: 'prev' | 'next') => {
    const newDate = new Date(currentDate);
    if (direction === 'prev') {
      newDate.setMonth(newDate.getMonth() - 1);
    } else {
      newDate.setMonth(newDate.getMonth() + 1);
    }
    setCurrentDate(newDate);
  };

  const getDaysInMonth = () => {
    const year = currentDate.getFullYear();
    const month = currentDate.getMonth();
    const firstDay = new Date(year, month, 1);
    const lastDay = new Date(year, month + 1, 0);
    const daysInMonth = lastDay.getDate();
    const startingDayOfWeek = firstDay.getDay();

    const days = [];
    
    // Add empty cells for days before the first day of the month
    for (let i = 0; i < startingDayOfWeek; i++) {
      days.push(null);
    }
    
    // Add days of the month
    for (let day = 1; day <= daysInMonth; day++) {
      days.push(day);
    }
    
    return days;
  };

  const getEventsForDate = (date: number) => {
    const dateStr = `${currentDate.getFullYear()}-${String(currentDate.getMonth() + 1).padStart(2, '0')}-${String(date).padStart(2, '0')}`;
    return events.filter(event => event.date === dateStr);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div className={`p-6 space-y-6 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* Header */}
      <div className={`flex justify-between items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
        <div>
          <ArabicTypography variant="h1" className="text-3xl font-bold text-gray-900">
            {language === 'ar' ? 'التقويم' : 'Calendar'}
          </ArabicTypography>
          <ArabicTypography variant="body1" className="text-gray-600 mt-1">
            {language === 'ar' ? 'إدارة جلسات الإرشاد والمواعيد' : 'Manage mentorship sessions and availability'}
          </ArabicTypography>
        </div>
        <div className={`flex gap-2 ${isRTL ? 'flex-row-reverse' : ''}`}>
          <ArabicButton variant="outline" icon={<Filter className="w-4 h-4" />}>
            {language === 'ar' ? 'تصفية' : 'Filter'}
          </ArabicButton>
          <ArabicButton variant="outline" icon={<Download className="w-4 h-4" />}>
            {language === 'ar' ? 'تصدير' : 'Export'}
          </ArabicButton>
          <ArabicButton icon={<Plus className="w-4 h-4" />}>
            {language === 'ar' ? 'إضافة موعد' : 'Add Event'}
          </ArabicButton>
        </div>
      </div>

      {/* View Mode Selector */}
      <div className={`flex gap-2 ${isRTL ? 'flex-row-reverse' : ''}`}>
        {['month', 'week', 'day'].map((mode) => (
          <ArabicButton
            key={mode}
            variant={viewMode === mode ? 'default' : 'outline'}
            onClick={() => setViewMode(mode as any)}
          >
            {language === 'ar' 
              ? (mode === 'month' ? 'شهر' : mode === 'week' ? 'أسبوع' : 'يوم')
              : mode.charAt(0).toUpperCase() + mode.slice(1)
            }
          </ArabicButton>
        ))}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Calendar */}
        <div className="lg:col-span-2">
          <ArabicCard>
            <div className="p-6">
              {/* Calendar Header */}
              <div className={`flex justify-between items-center mb-6 ${isRTL ? 'flex-row-reverse' : ''}`}>
                <ArabicButton
                  variant="outline"
                  size="sm"
                  onClick={() => navigateMonth('prev')}
                  icon={<ChevronLeft className="w-4 h-4" />}
                />
                <ArabicTypography variant="h2" className="text-xl font-semibold">
                  {currentDate.toLocaleDateString(language === 'ar' ? 'ar-EG' : 'en-US', { 
                    month: 'long', 
                    year: 'numeric' 
                  })}
                </ArabicTypography>
                <ArabicButton
                  variant="outline"
                  size="sm"
                  onClick={() => navigateMonth('next')}
                  icon={<ChevronRight className="w-4 h-4" />}
                />
              </div>

              {/* Calendar Grid */}
              <div className="grid grid-cols-7 gap-1">
                {/* Day Headers */}
                {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map((day) => (
                  <div key={day} className="p-2 text-center text-sm font-medium text-gray-500">
                    {language === 'ar' 
                      ? ['أحد', 'اثنين', 'ثلاثاء', 'أربعاء', 'خميس', 'جمعة', 'سبت'][['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].indexOf(day)]
                      : day
                    }
                  </div>
                ))}

                {/* Calendar Days */}
                {getDaysInMonth().map((day, index) => (
                  <div
                    key={index}
                    className={`min-h-[80px] p-1 border border-gray-200 ${
                      day ? 'cursor-pointer hover:bg-gray-50' : ''
                    }`}
                    onClick={() => day && setSelectedDate(new Date(currentDate.getFullYear(), currentDate.getMonth(), day))}
                  >
                    {day && (
                      <>
                        <div className="text-sm font-medium mb-1">{day}</div>
                        {getEventsForDate(day).map((event) => (
                          <div
                            key={event.id}
                            className={`text-xs p-1 rounded mb-1 ${getStatusColor(event.status)}`}
                          >
                            <div className="flex items-center gap-1">
                              {getEventTypeIcon(event.type)}
                              <span className="truncate">{event.startTime}</span>
                            </div>
                          </div>
                        ))}
                      </>
                    )}
                  </div>
                ))}
              </div>
            </div>
          </ArabicCard>
        </div>

        {/* Upcoming Events */}
        <div>
          <ArabicCard>
            <div className="p-6">
              <ArabicTypography variant="h2" className="text-xl font-semibold mb-4">
                {language === 'ar' ? 'المواعيد القادمة' : 'Upcoming Events'}
              </ArabicTypography>
              
              <div className="space-y-3">
                {events.slice(0, 5).map((event) => (
                  <div key={event.id} className="p-3 border border-gray-200 rounded-lg">
                    <div className={`flex justify-between items-start ${isRTL ? 'flex-row-reverse' : ''}`}>
                      <div className="flex-1">
                        <ArabicTypography variant="body1" className="font-medium">
                          {event.title}
                        </ArabicTypography>
                        <div className={`flex items-center gap-2 mt-1 ${isRTL ? 'flex-row-reverse' : ''}`}>
                          <User className="w-3 h-3 text-gray-500" />
                          <ArabicTypography variant="body2" className="text-gray-600">
                            {event.menteeName}
                          </ArabicTypography>
                        </div>
                        <div className={`flex items-center gap-2 mt-1 ${isRTL ? 'flex-row-reverse' : ''}`}>
                          <Clock className="w-3 h-3 text-gray-500" />
                          <ArabicTypography variant="body2" className="text-gray-600">
                            {event.startTime} - {event.endTime}
                          </ArabicTypography>
                        </div>
                        <div className={`flex items-center gap-2 mt-1 ${isRTL ? 'flex-row-reverse' : ''}`}>
                          {getEventTypeIcon(event.type)}
                          <ArabicTypography variant="body2" className="text-gray-600">
                            {language === 'ar' 
                              ? (event.type === 'video' ? 'فيديو' : 
                                 event.type === 'phone' ? 'هاتف' : 'شخصي')
                              : event.type
                            }
                          </ArabicTypography>
                        </div>
                      </div>
                      <div className={`flex gap-1 ${isRTL ? 'flex-row-reverse' : ''}`}>
                        <ArabicButton size="sm" variant="outline" icon={<Edit className="w-3 h-3" />} />
                        <ArabicButton size="sm" variant="outline" icon={<Trash2 className="w-3 h-3" />} />
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </ArabicCard>
        </div>
      </div>
    </div>
  );
};

export default MentorCalendarPage;
