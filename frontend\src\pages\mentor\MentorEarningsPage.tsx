/**
 * Mentor Earnings Page
 * Track mentorship earnings and payment history
 */

import React, { useState, useEffect } from 'react';
import { useLanguage } from '../../hooks/useLanguage';
import { ArabicCard, ArabicButton, ArabicTypography } from '../../components/ui/ArabicOptimizedComponents';
import { 
  DollarSign, 
  TrendingUp, 
  Calendar, 
  Download,
  Eye,
  Clock,
  CheckCircle,
  AlertCircle,
  CreditCard,
  BarChart3,
  PieChart,
  Filter
} from 'lucide-react';

interface EarningsData {
  id: string;
  sessionId: string;
  menteeName: string;
  sessionDate: string;
  duration: number;
  hourlyRate: number;
  totalAmount: number;
  status: 'pending' | 'paid' | 'processing';
  paymentDate?: string;
  sessionType: 'video' | 'phone' | 'in-person';
}

interface EarningsStats {
  totalEarnings: number;
  monthlyEarnings: number;
  pendingPayments: number;
  completedSessions: number;
  averageSessionRate: number;
  topPerformingMonth: string;
}

const MentorEarningsPage: React.FC = () => {
  const { language, isRTL } = useLanguage();
  const [earnings, setEarnings] = useState<EarningsData[]>([]);
  const [stats, setStats] = useState<EarningsStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [selectedPeriod, setSelectedPeriod] = useState('all');
  const [statusFilter, setStatusFilter] = useState('all');

  useEffect(() => {
    loadEarningsData();
  }, [selectedPeriod, statusFilter]);

  const loadEarningsData = async () => {
    setLoading(true);
    try {
      // TODO: Replace with real API call
      const mockEarnings: EarningsData[] = [
        {
          id: '1',
          sessionId: 'S001',
          menteeName: 'Ahmed Hassan',
          sessionDate: '2024-01-15',
          duration: 60,
          hourlyRate: 50,
          totalAmount: 50,
          status: 'paid',
          paymentDate: '2024-01-16',
          sessionType: 'video'
        },
        {
          id: '2',
          sessionId: 'S002',
          menteeName: 'Sara Mohamed',
          sessionDate: '2024-01-20',
          duration: 90,
          hourlyRate: 50,
          totalAmount: 75,
          status: 'pending',
          sessionType: 'phone'
        }
      ];

      const mockStats: EarningsStats = {
        totalEarnings: 2450,
        monthlyEarnings: 850,
        pendingPayments: 275,
        completedSessions: 48,
        averageSessionRate: 52,
        topPerformingMonth: 'December 2023'
      };

      setEarnings(mockEarnings);
      setStats(mockStats);
    } catch (error) {
      console.error('Error loading earnings data:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'paid': return 'bg-green-100 text-green-800';
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'processing': return 'bg-blue-100 text-blue-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'paid': return <CheckCircle className="w-4 h-4" />;
      case 'pending': return <Clock className="w-4 h-4" />;
      case 'processing': return <AlertCircle className="w-4 h-4" />;
      default: return <Clock className="w-4 h-4" />;
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div className={`p-6 space-y-6 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* Header */}
      <div className={`flex justify-between items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
        <div>
          <ArabicTypography variant="h1" className="text-3xl font-bold text-gray-900">
            {language === 'ar' ? 'الأرباح' : 'Earnings'}
          </ArabicTypography>
          <ArabicTypography variant="body1" className="text-gray-600 mt-1">
            {language === 'ar' ? 'تتبع أرباحك من جلسات الإرشاد' : 'Track your mentorship earnings'}
          </ArabicTypography>
        </div>
        <div className={`flex gap-2 ${isRTL ? 'flex-row-reverse' : ''}`}>
          <ArabicButton variant="outline" icon={<Download className="w-4 h-4" />}>
            {language === 'ar' ? 'تصدير' : 'Export'}
          </ArabicButton>
          <ArabicButton variant="outline" icon={<Filter className="w-4 h-4" />}>
            {language === 'ar' ? 'تصفية' : 'Filter'}
          </ArabicButton>
        </div>
      </div>

      {/* Stats Cards */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <ArabicCard>
            <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
              <div className="p-3 bg-green-100 rounded-lg">
                <DollarSign className="w-6 h-6 text-green-600" />
              </div>
              <div className={`${isRTL ? 'mr-4' : 'ml-4'}`}>
                <ArabicTypography variant="body2" className="text-gray-600">
                  {language === 'ar' ? 'إجمالي الأرباح' : 'Total Earnings'}
                </ArabicTypography>
                <ArabicTypography variant="h3" className="text-2xl font-bold">
                  ${stats.totalEarnings}
                </ArabicTypography>
              </div>
            </div>
          </ArabicCard>

          <ArabicCard>
            <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
              <div className="p-3 bg-blue-100 rounded-lg">
                <TrendingUp className="w-6 h-6 text-blue-600" />
              </div>
              <div className={`${isRTL ? 'mr-4' : 'ml-4'}`}>
                <ArabicTypography variant="body2" className="text-gray-600">
                  {language === 'ar' ? 'أرباح هذا الشهر' : 'This Month'}
                </ArabicTypography>
                <ArabicTypography variant="h3" className="text-2xl font-bold">
                  ${stats.monthlyEarnings}
                </ArabicTypography>
              </div>
            </div>
          </ArabicCard>

          <ArabicCard>
            <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
              <div className="p-3 bg-yellow-100 rounded-lg">
                <Clock className="w-6 h-6 text-yellow-600" />
              </div>
              <div className={`${isRTL ? 'mr-4' : 'ml-4'}`}>
                <ArabicTypography variant="body2" className="text-gray-600">
                  {language === 'ar' ? 'مدفوعات معلقة' : 'Pending Payments'}
                </ArabicTypography>
                <ArabicTypography variant="h3" className="text-2xl font-bold">
                  ${stats.pendingPayments}
                </ArabicTypography>
              </div>
            </div>
          </ArabicCard>

          <ArabicCard>
            <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
              <div className="p-3 bg-purple-100 rounded-lg">
                <BarChart3 className="w-6 h-6 text-purple-600" />
              </div>
              <div className={`${isRTL ? 'mr-4' : 'ml-4'}`}>
                <ArabicTypography variant="body2" className="text-gray-600">
                  {language === 'ar' ? 'متوسط السعر' : 'Avg. Rate'}
                </ArabicTypography>
                <ArabicTypography variant="h3" className="text-2xl font-bold">
                  ${stats.averageSessionRate}/hr
                </ArabicTypography>
              </div>
            </div>
          </ArabicCard>
        </div>
      )}

      {/* Earnings Table */}
      <ArabicCard>
        <div className="p-6">
          <ArabicTypography variant="h2" className="text-xl font-semibold mb-4">
            {language === 'ar' ? 'تاريخ الأرباح' : 'Earnings History'}
          </ArabicTypography>
          
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b">
                  <th className={`py-3 px-4 text-left ${isRTL ? 'text-right' : 'text-left'}`}>
                    {language === 'ar' ? 'المتدرب' : 'Mentee'}
                  </th>
                  <th className={`py-3 px-4 text-left ${isRTL ? 'text-right' : 'text-left'}`}>
                    {language === 'ar' ? 'التاريخ' : 'Date'}
                  </th>
                  <th className={`py-3 px-4 text-left ${isRTL ? 'text-right' : 'text-left'}`}>
                    {language === 'ar' ? 'المدة' : 'Duration'}
                  </th>
                  <th className={`py-3 px-4 text-left ${isRTL ? 'text-right' : 'text-left'}`}>
                    {language === 'ar' ? 'المبلغ' : 'Amount'}
                  </th>
                  <th className={`py-3 px-4 text-left ${isRTL ? 'text-right' : 'text-left'}`}>
                    {language === 'ar' ? 'الحالة' : 'Status'}
                  </th>
                  <th className={`py-3 px-4 text-left ${isRTL ? 'text-right' : 'text-left'}`}>
                    {language === 'ar' ? 'إجراءات' : 'Actions'}
                  </th>
                </tr>
              </thead>
              <tbody>
                {earnings.map((earning) => (
                  <tr key={earning.id} className="border-b hover:bg-gray-50">
                    <td className="py-3 px-4">
                      <ArabicTypography variant="body1" className="font-medium">
                        {earning.menteeName}
                      </ArabicTypography>
                    </td>
                    <td className="py-3 px-4">
                      <ArabicTypography variant="body2" className="text-gray-600">
                        {new Date(earning.sessionDate).toLocaleDateString()}
                      </ArabicTypography>
                    </td>
                    <td className="py-3 px-4">
                      <ArabicTypography variant="body2">
                        {earning.duration} {language === 'ar' ? 'دقيقة' : 'min'}
                      </ArabicTypography>
                    </td>
                    <td className="py-3 px-4">
                      <ArabicTypography variant="body1" className="font-semibold">
                        ${earning.totalAmount}
                      </ArabicTypography>
                    </td>
                    <td className="py-3 px-4">
                      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(earning.status)}`}>
                        {getStatusIcon(earning.status)}
                        <span className={`${isRTL ? 'mr-1' : 'ml-1'}`}>
                          {language === 'ar' 
                            ? (earning.status === 'paid' ? 'مدفوع' : 
                               earning.status === 'pending' ? 'معلق' : 'قيد المعالجة')
                            : earning.status
                          }
                        </span>
                      </span>
                    </td>
                    <td className="py-3 px-4">
                      <ArabicButton size="sm" variant="outline" icon={<Eye className="w-3 h-3" />}>
                        {language === 'ar' ? 'عرض' : 'View'}
                      </ArabicButton>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </ArabicCard>
    </div>
  );
};

export default MentorEarningsPage;
