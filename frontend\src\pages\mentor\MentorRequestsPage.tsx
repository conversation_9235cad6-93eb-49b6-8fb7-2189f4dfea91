/**
 * Mentor Requests Page
 * Manage incoming mentorship requests
 */

import React, { useState, useEffect } from 'react';
import { useLanguage } from '../../hooks/useLanguage';
import { ArabicCard, ArabicButton, ArabicTypography } from '../../components/ui/ArabicOptimizedComponents';
import { 
  UserPlus, 
  Clock, 
  CheckCircle, 
  XCircle,
  Eye,
  MessageSquare,
  Calendar,
  User,
  Star,
  Filter,
  Search,
  RefreshCw
} from 'lucide-react';

interface MentorshipRequest {
  id: string;
  applicantName: string;
  applicantEmail: string;
  applicantTitle: string;
  company?: string;
  experience: string;
  goals: string;
  preferredSchedule: string;
  sessionType: 'video' | 'phone' | 'in-person';
  status: 'pending' | 'approved' | 'rejected' | 'interview_scheduled';
  submittedAt: string;
  urgency: 'low' | 'medium' | 'high';
  background: string;
  expectations: string;
  avatar?: string;
}

interface RequestStats {
  totalRequests: number;
  pendingRequests: number;
  approvedThisMonth: number;
  averageResponseTime: string;
  acceptanceRate: number;
}

const MentorRequestsPage: React.FC = () => {
  const { language, isRTL } = useLanguage();
  const [requests, setRequests] = useState<MentorshipRequest[]>([]);
  const [stats, setStats] = useState<RequestStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [selectedFilter, setSelectedFilter] = useState('pending');
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    loadRequestsData();
  }, [selectedFilter]);

  const loadRequestsData = async () => {
    setLoading(true);
    try {
      // TODO: Replace with real API call
      const mockRequests: MentorshipRequest[] = [
        {
          id: '1',
          applicantName: 'Ahmed Hassan',
          applicantEmail: '<EMAIL>',
          applicantTitle: 'Junior Developer',
          company: 'TechStart Syria',
          experience: '2 years',
          goals: 'Learn advanced React patterns and system design',
          preferredSchedule: 'Weekends, 2-4 PM',
          sessionType: 'video',
          status: 'pending',
          submittedAt: '2024-01-15T10:30:00Z',
          urgency: 'medium',
          background: 'Computer Science graduate with focus on web development',
          expectations: 'Weekly 1-hour sessions for 3 months'
        },
        {
          id: '2',
          applicantName: 'Sara Mohamed',
          applicantEmail: '<EMAIL>',
          applicantTitle: 'Product Manager',
          company: 'Innovation Hub',
          experience: '3 years',
          goals: 'Transition to tech leadership role',
          preferredSchedule: 'Weekdays, 6-8 PM',
          sessionType: 'phone',
          status: 'interview_scheduled',
          submittedAt: '2024-01-14T14:20:00Z',
          urgency: 'high',
          background: 'Business background transitioning to tech',
          expectations: 'Bi-weekly sessions focusing on leadership skills'
        }
      ];

      const mockStats: RequestStats = {
        totalRequests: 45,
        pendingRequests: 8,
        approvedThisMonth: 12,
        averageResponseTime: '24 hours',
        acceptanceRate: 75
      };

      setRequests(mockRequests);
      setStats(mockStats);
    } catch (error) {
      console.error('Error loading requests data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleRequestAction = async (requestId: string, action: 'approve' | 'reject' | 'schedule') => {
    try {
      // TODO: Replace with real API call
      console.log(`${action} request ${requestId}`);
      
      setRequests(prev => prev.map(req => 
        req.id === requestId 
          ? { ...req, status: action === 'approve' ? 'approved' : action === 'reject' ? 'rejected' : 'interview_scheduled' }
          : req
      ));
    } catch (error) {
      console.error(`Error ${action}ing request:`, error);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'approved': return 'bg-green-100 text-green-800';
      case 'rejected': return 'bg-red-100 text-red-800';
      case 'interview_scheduled': return 'bg-blue-100 text-blue-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getUrgencyColor = (urgency: string) => {
    switch (urgency) {
      case 'high': return 'bg-red-100 text-red-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      case 'low': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending': return <Clock className="w-4 h-4" />;
      case 'approved': return <CheckCircle className="w-4 h-4" />;
      case 'rejected': return <XCircle className="w-4 h-4" />;
      case 'interview_scheduled': return <Calendar className="w-4 h-4" />;
      default: return <Clock className="w-4 h-4" />;
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div className={`p-6 space-y-6 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* Header */}
      <div className={`flex justify-between items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
        <div>
          <ArabicTypography variant="h1" className="text-3xl font-bold text-gray-900">
            {language === 'ar' ? 'طلبات الإرشاد' : 'Mentorship Requests'}
          </ArabicTypography>
          <ArabicTypography variant="body1" className="text-gray-600 mt-1">
            {language === 'ar' ? 'إدارة طلبات الإرشاد الواردة' : 'Manage incoming mentorship requests'}
          </ArabicTypography>
        </div>
        <div className={`flex gap-2 ${isRTL ? 'flex-row-reverse' : ''}`}>
          <ArabicButton variant="outline" icon={<RefreshCw className="w-4 h-4" />} onClick={loadRequestsData}>
            {language === 'ar' ? 'تحديث' : 'Refresh'}
          </ArabicButton>
          <ArabicButton variant="outline" icon={<Filter className="w-4 h-4" />}>
            {language === 'ar' ? 'تصفية' : 'Filter'}
          </ArabicButton>
        </div>
      </div>

      {/* Stats Cards */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <ArabicCard>
            <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
              <div className="p-3 bg-blue-100 rounded-lg">
                <UserPlus className="w-6 h-6 text-blue-600" />
              </div>
              <div className={`${isRTL ? 'mr-4' : 'ml-4'}`}>
                <ArabicTypography variant="body2" className="text-gray-600">
                  {language === 'ar' ? 'إجمالي الطلبات' : 'Total Requests'}
                </ArabicTypography>
                <ArabicTypography variant="h3" className="text-2xl font-bold">
                  {stats.totalRequests}
                </ArabicTypography>
              </div>
            </div>
          </ArabicCard>

          <ArabicCard>
            <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
              <div className="p-3 bg-yellow-100 rounded-lg">
                <Clock className="w-6 h-6 text-yellow-600" />
              </div>
              <div className={`${isRTL ? 'mr-4' : 'ml-4'}`}>
                <ArabicTypography variant="body2" className="text-gray-600">
                  {language === 'ar' ? 'طلبات معلقة' : 'Pending Requests'}
                </ArabicTypography>
                <ArabicTypography variant="h3" className="text-2xl font-bold text-yellow-600">
                  {stats.pendingRequests}
                </ArabicTypography>
              </div>
            </div>
          </ArabicCard>

          <ArabicCard>
            <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
              <div className="p-3 bg-green-100 rounded-lg">
                <CheckCircle className="w-6 h-6 text-green-600" />
              </div>
              <div className={`${isRTL ? 'mr-4' : 'ml-4'}`}>
                <ArabicTypography variant="body2" className="text-gray-600">
                  {language === 'ar' ? 'تمت الموافقة هذا الشهر' : 'Approved This Month'}
                </ArabicTypography>
                <ArabicTypography variant="h3" className="text-2xl font-bold text-green-600">
                  {stats.approvedThisMonth}
                </ArabicTypography>
              </div>
            </div>
          </ArabicCard>

          <ArabicCard>
            <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
              <div className="p-3 bg-purple-100 rounded-lg">
                <Star className="w-6 h-6 text-purple-600" />
              </div>
              <div className={`${isRTL ? 'mr-4' : 'ml-4'}`}>
                <ArabicTypography variant="body2" className="text-gray-600">
                  {language === 'ar' ? 'معدل القبول' : 'Acceptance Rate'}
                </ArabicTypography>
                <ArabicTypography variant="h3" className="text-2xl font-bold text-purple-600">
                  {stats.acceptanceRate}%
                </ArabicTypography>
              </div>
            </div>
          </ArabicCard>
        </div>
      )}

      {/* Filters */}
      <ArabicCard>
        <div className="p-6">
          <div className={`flex gap-4 ${isRTL ? 'flex-row-reverse' : ''}`}>
            <div className="flex-1 relative">
              <Search className={`absolute top-3 ${isRTL ? 'right-3' : 'left-3'} w-4 h-4 text-gray-400`} />
              <input
                type="text"
                placeholder={language === 'ar' ? 'البحث في الطلبات...' : 'Search requests...'}
                className={`w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg ${isRTL ? 'text-right' : 'text-left'}`}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            <select
              className="px-4 py-2 border border-gray-300 rounded-lg"
              value={selectedFilter}
              onChange={(e) => setSelectedFilter(e.target.value)}
            >
              <option value="all">{language === 'ar' ? 'جميع الطلبات' : 'All Requests'}</option>
              <option value="pending">{language === 'ar' ? 'معلقة' : 'Pending'}</option>
              <option value="approved">{language === 'ar' ? 'موافق عليها' : 'Approved'}</option>
              <option value="interview_scheduled">{language === 'ar' ? 'مقابلة مجدولة' : 'Interview Scheduled'}</option>
              <option value="rejected">{language === 'ar' ? 'مرفوضة' : 'Rejected'}</option>
            </select>
          </div>
        </div>
      </ArabicCard>

      {/* Requests List */}
      <div className="space-y-4">
        {requests.map((request) => (
          <ArabicCard key={request.id}>
            <div className="p-6">
              <div className={`flex justify-between items-start ${isRTL ? 'flex-row-reverse' : ''}`}>
                <div className="flex-1">
                  <div className={`flex items-center gap-3 mb-3 ${isRTL ? 'flex-row-reverse' : ''}`}>
                    <div className="w-12 h-12 bg-gray-200 rounded-full flex items-center justify-center">
                      <User className="w-6 h-6 text-gray-600" />
                    </div>
                    <div>
                      <ArabicTypography variant="h3" className="text-lg font-semibold">
                        {request.applicantName}
                      </ArabicTypography>
                      <ArabicTypography variant="body2" className="text-gray-600">
                        {request.applicantTitle} {request.company && `at ${request.company}`}
                      </ArabicTypography>
                    </div>
                    <div className={`flex gap-2 ${isRTL ? 'flex-row-reverse' : ''}`}>
                      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(request.status)}`}>
                        {getStatusIcon(request.status)}
                        <span className={`${isRTL ? 'mr-1' : 'ml-1'}`}>
                          {language === 'ar' 
                            ? (request.status === 'pending' ? 'معلق' : 
                               request.status === 'approved' ? 'موافق عليه' : 
                               request.status === 'rejected' ? 'مرفوض' : 'مقابلة مجدولة')
                            : request.status.replace('_', ' ')
                          }
                        </span>
                      </span>
                      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getUrgencyColor(request.urgency)}`}>
                        {language === 'ar' 
                          ? (request.urgency === 'high' ? 'عالي' : 
                             request.urgency === 'medium' ? 'متوسط' : 'منخفض')
                          : request.urgency
                        }
                      </span>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div>
                      <ArabicTypography variant="body2" className="font-medium text-gray-700">
                        {language === 'ar' ? 'الأهداف:' : 'Goals:'}
                      </ArabicTypography>
                      <ArabicTypography variant="body2" className="text-gray-600">
                        {request.goals}
                      </ArabicTypography>
                    </div>
                    <div>
                      <ArabicTypography variant="body2" className="font-medium text-gray-700">
                        {language === 'ar' ? 'الخبرة:' : 'Experience:'}
                      </ArabicTypography>
                      <ArabicTypography variant="body2" className="text-gray-600">
                        {request.experience}
                      </ArabicTypography>
                    </div>
                    <div>
                      <ArabicTypography variant="body2" className="font-medium text-gray-700">
                        {language === 'ar' ? 'الجدولة المفضلة:' : 'Preferred Schedule:'}
                      </ArabicTypography>
                      <ArabicTypography variant="body2" className="text-gray-600">
                        {request.preferredSchedule}
                      </ArabicTypography>
                    </div>
                    <div>
                      <ArabicTypography variant="body2" className="font-medium text-gray-700">
                        {language === 'ar' ? 'نوع الجلسة:' : 'Session Type:'}
                      </ArabicTypography>
                      <ArabicTypography variant="body2" className="text-gray-600">
                        {language === 'ar' 
                          ? (request.sessionType === 'video' ? 'فيديو' : 
                             request.sessionType === 'phone' ? 'هاتف' : 'شخصي')
                          : request.sessionType
                        }
                      </ArabicTypography>
                    </div>
                  </div>
                </div>

                <div className={`flex gap-2 ${isRTL ? 'flex-row-reverse' : ''}`}>
                  <ArabicButton size="sm" variant="outline" icon={<Eye className="w-3 h-3" />}>
                    {language === 'ar' ? 'عرض' : 'View'}
                  </ArabicButton>
                  <ArabicButton size="sm" variant="outline" icon={<MessageSquare className="w-3 h-3" />}>
                    {language === 'ar' ? 'رسالة' : 'Message'}
                  </ArabicButton>
                  {request.status === 'pending' && (
                    <>
                      <ArabicButton 
                        size="sm" 
                        variant="outline"
                        icon={<Calendar className="w-3 h-3" />}
                        onClick={() => handleRequestAction(request.id, 'schedule')}
                      >
                        {language === 'ar' ? 'جدولة' : 'Schedule'}
                      </ArabicButton>
                      <ArabicButton 
                        size="sm" 
                        icon={<CheckCircle className="w-3 h-3" />}
                        onClick={() => handleRequestAction(request.id, 'approve')}
                      >
                        {language === 'ar' ? 'قبول' : 'Accept'}
                      </ArabicButton>
                      <ArabicButton 
                        size="sm" 
                        variant="outline"
                        icon={<XCircle className="w-3 h-3" />}
                        onClick={() => handleRequestAction(request.id, 'reject')}
                      >
                        {language === 'ar' ? 'رفض' : 'Decline'}
                      </ArabicButton>
                    </>
                  )}
                </div>
              </div>
            </div>
          </ArabicCard>
        ))}
      </div>
    </div>
  );
};

export default MentorRequestsPage;
