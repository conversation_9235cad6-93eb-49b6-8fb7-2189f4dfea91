import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import {
  Calendar,
  Clock,
  Video,
  Users,
  MessageSquare,
  Star,
  ArrowRight,
  Plus,
  CheckCircle,
  AlertCircle,
  RefreshCw,
  Search,
  Filter
} from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../hooks/useLanguage';
import AuthenticatedLayout from '../../components/layout/AuthenticatedLayout';

interface Session {
  id: string;
  menteeId: string;
  menteeName: string;
  title: string;
  description: string;
  scheduledDate: string;
  duration: number; // in minutes
  status: 'scheduled' | 'completed' | 'cancelled' | 'in-progress';
  type: 'video' | 'phone' | 'in-person';
  meetingLink?: string;
  notes?: string;
  rating?: number;
  feedback?: string;
}

const SessionsPage: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const navigate = useNavigate();

  const [sessions, setSessions] = useState<Session[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<'upcoming' | 'past'>('upcoming');
  const [searchTerm, setSearchTerm] = useState('');

  // Mock data - replace with actual API call
  useEffect(() => {
    const fetchSessions = async () => {
      setIsLoading(true);
      try {
        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        const mockSessions: Session[] = [
          {
            id: '1',
            menteeId: '1',
            menteeName: 'أحمد محمد',
            title: 'مراجعة خطة العمل',
            description: 'مناقشة التطورات في خطة العمل والخطوات التالية',
            scheduledDate: '2024-01-25T10:00:00Z',
            duration: 60,
            status: 'scheduled',
            type: 'video',
            meetingLink: 'https://meet.example.com/session1'
          },
          {
            id: '2',
            menteeId: '2',
            menteeName: 'فاطمة علي',
            title: 'استراتيجية التسويق',
            description: 'تطوير استراتيجية التسويق للمنتج الجديد',
            scheduledDate: '2024-01-26T14:00:00Z',
            duration: 45,
            status: 'scheduled',
            type: 'video',
            meetingLink: 'https://meet.example.com/session2'
          },
          {
            id: '3',
            menteeId: '1',
            menteeName: 'أحمد محمد',
            title: 'جلسة المتابعة الأولى',
            description: 'مراجعة التقدم المحرز في الأسبوع الماضي',
            scheduledDate: '2024-01-18T10:00:00Z',
            duration: 60,
            status: 'completed',
            type: 'video',
            rating: 5,
            feedback: 'جلسة مفيدة جداً، حصلت على توجيهات واضحة',
            notes: 'المتدرب يظهر تقدماً جيداً في تطوير فكرته'
          }
        ];
        
        setSessions(mockSessions);
      } catch (error) {
        console.error('Error fetching sessions:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchSessions();
  }, []);

  const upcomingSessions = sessions.filter(session => 
    session.status === 'scheduled' || session.status === 'in-progress'
  );
  
  const pastSessions = sessions.filter(session => 
    session.status === 'completed' || session.status === 'cancelled'
  );

  const filteredSessions = (activeTab === 'upcoming' ? upcomingSessions : pastSessions)
    .filter(session => 
      session.menteeName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      session.title.toLowerCase().includes(searchTerm.toLowerCase())
    );

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'scheduled': return 'text-blue-400 bg-blue-400/10';
      case 'completed': return 'text-green-400 bg-green-400/10';
      case 'cancelled': return 'text-red-400 bg-red-400/10';
      case 'in-progress': return 'text-yellow-400 bg-yellow-400/10';
      default: return 'text-gray-400 bg-gray-400/10';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'scheduled': return <Calendar size={16} />;
      case 'completed': return <CheckCircle size={16} />;
      case 'cancelled': return <AlertCircle size={16} />;
      case 'in-progress': return <RefreshCw size={16} />;
      default: return <Clock size={16} />;
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'video': return <Video size={16} />;
      case 'phone': return <MessageSquare size={16} />;
      case 'in-person': return <Users size={16} />;
      default: return <Calendar size={16} />;
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('ar-SA', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <AuthenticatedLayout>
      <div className="min-h-screen bg-gradient-to-br from-indigo-900 via-purple-900 to-pink-900 p-6">
        <div className="max-w-7xl mx-auto">
          {/* Header */}
          <div className="mb-8">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
              <div>
                <h1 className="text-3xl font-bold text-white mb-2">
                  {t('mentor.sessions.title', 'Mentorship Sessions')}
                </h1>
                <p className="text-gray-300">
                  {t('mentor.sessions.subtitle', 'Manage your mentorship sessions and schedule')}
                </p>
              </div>
              <Link
                to="/mentor/sessions/schedule"
                className="inline-flex items-center px-4 py-2 bg-indigo-600 hover:bg-indigo-700 text-white rounded-lg transition-colors"
              >
                <Plus size={20} className={`${isRTL ? 'ml-2' : 'mr-2'}`} />
                {t('mentor.sessions.schedule', 'Schedule Session')}
              </Link>
            </div>
          </div>

          {/* Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-gray-300 text-sm">{t('mentor.stats.totalSessions', 'Total Sessions')}</p>
                  <p className="text-2xl font-bold text-white">{sessions.length}</p>
                </div>
                <Calendar className="text-indigo-400" size={24} />
              </div>
            </div>
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-gray-300 text-sm">{t('mentor.stats.upcoming', 'Upcoming')}</p>
                  <p className="text-2xl font-bold text-white">{upcomingSessions.length}</p>
                </div>
                <Clock className="text-blue-400" size={24} />
              </div>
            </div>
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-gray-300 text-sm">{t('mentor.stats.completed', 'Completed')}</p>
                  <p className="text-2xl font-bold text-white">
                    {sessions.filter(s => s.status === 'completed').length}
                  </p>
                </div>
                <CheckCircle className="text-green-400" size={24} />
              </div>
            </div>
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-gray-300 text-sm">{t('mentor.stats.avgRating', 'Avg Rating')}</p>
                  <p className="text-2xl font-bold text-white">
                    {(sessions.filter(s => s.rating).reduce((sum, s) => sum + (s.rating || 0), 0) / 
                      sessions.filter(s => s.rating).length || 0).toFixed(1)}
                  </p>
                </div>
                <Star className="text-yellow-400" size={24} />
              </div>
            </div>
          </div>

          {/* Tabs and Search */}
          <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 mb-6 border border-white/20">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
              <div className="flex space-x-1 bg-white/10 rounded-lg p-1">
                <button
                  onClick={() => setActiveTab('upcoming')}
                  className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                    activeTab === 'upcoming'
                      ? 'bg-indigo-600 text-white'
                      : 'text-gray-300 hover:text-white'
                  }`}
                >
                  {t('mentor.sessions.upcoming', 'Upcoming')} ({upcomingSessions.length})
                </button>
                <button
                  onClick={() => setActiveTab('past')}
                  className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                    activeTab === 'past'
                      ? 'bg-indigo-600 text-white'
                      : 'text-gray-300 hover:text-white'
                  }`}
                >
                  {t('mentor.sessions.past', 'Past')} ({pastSessions.length})
                </button>
              </div>
              <div className="flex items-center gap-2">
                <div className="relative">
                  <Search size={20} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                  <input
                    type="text"
                    placeholder={t('mentor.sessions.search', 'Search sessions...')}
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10 pr-4 py-2 bg-white/10 border border-white/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-indigo-500"
                  />
                </div>
              </div>
            </div>
          </div>

          {/* Sessions List */}
          <div className="bg-white/10 backdrop-blur-sm rounded-lg border border-white/20 overflow-hidden">
            {isLoading ? (
              <div className="p-8 text-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white mx-auto"></div>
                <p className="text-gray-300 mt-2">{t('common.loading', 'Loading...')}</p>
              </div>
            ) : filteredSessions.length === 0 ? (
              <div className="p-8 text-center">
                <Calendar size={48} className="mx-auto text-gray-500 mb-4" />
                <h3 className="text-lg font-medium text-white mb-2">
                  {t('mentor.sessions.noSessions', 'No sessions found')}
                </h3>
                <p className="text-gray-400">
                  {activeTab === 'upcoming' 
                    ? t('mentor.sessions.noUpcoming', 'No upcoming sessions scheduled')
                    : t('mentor.sessions.noPast', 'No past sessions found')
                  }
                </p>
              </div>
            ) : (
              <div className="divide-y divide-white/10">
                {filteredSessions.map((session) => (
                  <div key={session.id} className="p-6 hover:bg-white/5 transition-colors">
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-3 mb-2">
                          <h3 className="text-lg font-semibold text-white">{session.title}</h3>
                          <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(session.status)}`}>
                            {getStatusIcon(session.status)}
                            <span className="ml-1">{t(`status.${session.status}`, session.status)}</span>
                          </span>
                          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium text-gray-300 bg-gray-700">
                            {getTypeIcon(session.type)}
                            <span className="ml-1">{t(`sessionType.${session.type}`, session.type)}</span>
                          </span>
                        </div>
                        <p className="text-gray-300 mb-2">{session.description}</p>
                        <div className="flex items-center space-x-4 text-sm text-gray-400">
                          <span className="flex items-center">
                            <Users size={16} className="mr-1" />
                            {session.menteeName}
                          </span>
                          <span className="flex items-center">
                            <Clock size={16} className="mr-1" />
                            {formatDate(session.scheduledDate)}
                          </span>
                          <span>{session.duration} {t('common.minutes', 'minutes')}</span>
                          {session.rating && (
                            <span className="flex items-center">
                              <Star size={16} className="mr-1 text-yellow-400" />
                              {session.rating}/5
                            </span>
                          )}
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        {session.status === 'scheduled' && session.meetingLink && (
                          <a
                            href={session.meetingLink}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="inline-flex items-center px-3 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors"
                          >
                            <Video size={16} className={`${isRTL ? 'ml-2' : 'mr-2'}`} />
                            {t('mentor.sessions.join', 'Join')}
                          </a>
                        )}
                        <Link
                          to={`/mentor/sessions/${session.id}`}
                          className="inline-flex items-center px-3 py-2 bg-indigo-600 hover:bg-indigo-700 text-white rounded-lg transition-colors"
                        >
                          {t('common.view', 'View')}
                          <ArrowRight size={16} className={`${isRTL ? 'mr-2' : 'ml-2'}`} />
                        </Link>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
    </AuthenticatedLayout>
  );
};

export default SessionsPage;
