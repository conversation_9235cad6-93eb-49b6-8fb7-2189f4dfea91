/**
 * Moderation Dashboard Page
 * Central hub for content moderation activities
 */

import React, { useState, useEffect } from 'react';
import { useLanguage } from '../../hooks/useLanguage';
import { ArabicCard, ArabicButton, ArabicTypography } from '../../components/ui/ArabicOptimizedComponents';
import { 
  Shield, 
  AlertTriangle, 
  CheckCircle, 
  Clock,
  Eye,
  Flag,
  MessageSquare,
  Users,
  TrendingUp,
  BarChart3,
  Filter,
  Search,
  RefreshCw
} from 'lucide-react';

interface ModerationStats {
  pendingReports: number;
  resolvedToday: number;
  totalReports: number;
  averageResponseTime: string;
  activeFlags: number;
  contentRemoved: number;
  usersWarned: number;
  usersBanned: number;
}

interface ReportItem {
  id: string;
  type: 'post' | 'comment' | 'user' | 'message';
  content: string;
  reportedBy: string;
  reportedUser: string;
  reason: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  status: 'pending' | 'reviewing' | 'resolved' | 'dismissed';
  createdAt: string;
  assignedTo?: string;
}

const ModerationDashboardPage: React.FC = () => {
  const { language, isRTL } = useLanguage();
  const [stats, setStats] = useState<ModerationStats | null>(null);
  const [reports, setReports] = useState<ReportItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedFilter, setSelectedFilter] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    loadModerationData();
  }, [selectedFilter]);

  const loadModerationData = async () => {
    setLoading(true);
    try {
      // TODO: Replace with real API call
      const mockStats: ModerationStats = {
        pendingReports: 12,
        resolvedToday: 8,
        totalReports: 156,
        averageResponseTime: '2.5 hours',
        activeFlags: 5,
        contentRemoved: 23,
        usersWarned: 15,
        usersBanned: 3
      };

      const mockReports: ReportItem[] = [
        {
          id: '1',
          type: 'post',
          content: 'Inappropriate business idea post with offensive language...',
          reportedBy: 'Ahmed Hassan',
          reportedUser: 'John Doe',
          reason: 'Inappropriate content',
          severity: 'high',
          status: 'pending',
          createdAt: '2024-01-15T10:30:00Z'
        },
        {
          id: '2',
          type: 'comment',
          content: 'Spam comment promoting external services...',
          reportedBy: 'Sara Mohamed',
          reportedUser: 'Jane Smith',
          reason: 'Spam',
          severity: 'medium',
          status: 'reviewing',
          createdAt: '2024-01-15T09:15:00Z',
          assignedTo: 'Moderator 1'
        }
      ];

      setStats(mockStats);
      setReports(mockReports);
    } catch (error) {
      console.error('Error loading moderation data:', error);
    } finally {
      setLoading(false);
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical': return 'bg-red-100 text-red-800 border-red-200';
      case 'high': return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'medium': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'low': return 'bg-blue-100 text-blue-800 border-blue-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'reviewing': return 'bg-blue-100 text-blue-800';
      case 'resolved': return 'bg-green-100 text-green-800';
      case 'dismissed': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'post': return <MessageSquare className="w-4 h-4" />;
      case 'comment': return <MessageSquare className="w-4 h-4" />;
      case 'user': return <Users className="w-4 h-4" />;
      case 'message': return <MessageSquare className="w-4 h-4" />;
      default: return <Flag className="w-4 h-4" />;
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div className={`p-6 space-y-6 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* Header */}
      <div className={`flex justify-between items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
        <div>
          <ArabicTypography variant="h1" className="text-3xl font-bold text-gray-900">
            {language === 'ar' ? 'لوحة الإشراف' : 'Moderation Dashboard'}
          </ArabicTypography>
          <ArabicTypography variant="body1" className="text-gray-600 mt-1">
            {language === 'ar' ? 'إدارة ومراقبة المحتوى والمستخدمين' : 'Manage and monitor content and users'}
          </ArabicTypography>
        </div>
        <div className={`flex gap-2 ${isRTL ? 'flex-row-reverse' : ''}`}>
          <ArabicButton variant="outline" icon={<RefreshCw className="w-4 h-4" />} onClick={loadModerationData}>
            {language === 'ar' ? 'تحديث' : 'Refresh'}
          </ArabicButton>
          <ArabicButton variant="outline" icon={<Filter className="w-4 h-4" />}>
            {language === 'ar' ? 'تصفية' : 'Filter'}
          </ArabicButton>
        </div>
      </div>

      {/* Stats Cards */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <ArabicCard>
            <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
              <div className="p-3 bg-red-100 rounded-lg">
                <AlertTriangle className="w-6 h-6 text-red-600" />
              </div>
              <div className={`${isRTL ? 'mr-4' : 'ml-4'}`}>
                <ArabicTypography variant="body2" className="text-gray-600">
                  {language === 'ar' ? 'تقارير معلقة' : 'Pending Reports'}
                </ArabicTypography>
                <ArabicTypography variant="h3" className="text-2xl font-bold text-red-600">
                  {stats.pendingReports}
                </ArabicTypography>
              </div>
            </div>
          </ArabicCard>

          <ArabicCard>
            <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
              <div className="p-3 bg-green-100 rounded-lg">
                <CheckCircle className="w-6 h-6 text-green-600" />
              </div>
              <div className={`${isRTL ? 'mr-4' : 'ml-4'}`}>
                <ArabicTypography variant="body2" className="text-gray-600">
                  {language === 'ar' ? 'تم حلها اليوم' : 'Resolved Today'}
                </ArabicTypography>
                <ArabicTypography variant="h3" className="text-2xl font-bold text-green-600">
                  {stats.resolvedToday}
                </ArabicTypography>
              </div>
            </div>
          </ArabicCard>

          <ArabicCard>
            <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
              <div className="p-3 bg-blue-100 rounded-lg">
                <Clock className="w-6 h-6 text-blue-600" />
              </div>
              <div className={`${isRTL ? 'mr-4' : 'ml-4'}`}>
                <ArabicTypography variant="body2" className="text-gray-600">
                  {language === 'ar' ? 'متوسط وقت الاستجابة' : 'Avg Response Time'}
                </ArabicTypography>
                <ArabicTypography variant="h3" className="text-2xl font-bold">
                  {stats.averageResponseTime}
                </ArabicTypography>
              </div>
            </div>
          </ArabicCard>

          <ArabicCard>
            <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
              <div className="p-3 bg-purple-100 rounded-lg">
                <Shield className="w-6 h-6 text-purple-600" />
              </div>
              <div className={`${isRTL ? 'mr-4' : 'ml-4'}`}>
                <ArabicTypography variant="body2" className="text-gray-600">
                  {language === 'ar' ? 'إجراءات اتخذت' : 'Actions Taken'}
                </ArabicTypography>
                <ArabicTypography variant="h3" className="text-2xl font-bold">
                  {stats.contentRemoved + stats.usersWarned + stats.usersBanned}
                </ArabicTypography>
              </div>
            </div>
          </ArabicCard>
        </div>
      )}

      {/* Quick Actions */}
      <ArabicCard>
        <div className="p-6">
          <ArabicTypography variant="h2" className="text-xl font-semibold mb-4">
            {language === 'ar' ? 'إجراءات سريعة' : 'Quick Actions'}
          </ArabicTypography>
          <div className={`grid grid-cols-1 md:grid-cols-3 gap-4 ${isRTL ? 'text-right' : 'text-left'}`}>
            <ArabicButton variant="outline" className="h-20 flex-col">
              <Flag className="w-6 h-6 mb-2" />
              {language === 'ar' ? 'مراجعة التقارير' : 'Review Reports'}
            </ArabicButton>
            <ArabicButton variant="outline" className="h-20 flex-col">
              <Users className="w-6 h-6 mb-2" />
              {language === 'ar' ? 'إدارة المستخدمين' : 'Manage Users'}
            </ArabicButton>
            <ArabicButton variant="outline" className="h-20 flex-col">
              <BarChart3 className="w-6 h-6 mb-2" />
              {language === 'ar' ? 'عرض التحليلات' : 'View Analytics'}
            </ArabicButton>
          </div>
        </div>
      </ArabicCard>

      {/* Recent Reports */}
      <ArabicCard>
        <div className="p-6">
          <div className={`flex justify-between items-center mb-4 ${isRTL ? 'flex-row-reverse' : ''}`}>
            <ArabicTypography variant="h2" className="text-xl font-semibold">
              {language === 'ar' ? 'التقارير الأخيرة' : 'Recent Reports'}
            </ArabicTypography>
            <div className={`flex gap-2 ${isRTL ? 'flex-row-reverse' : ''}`}>
              <div className="relative">
                <Search className={`absolute top-3 ${isRTL ? 'right-3' : 'left-3'} w-4 h-4 text-gray-400`} />
                <input
                  type="text"
                  placeholder={language === 'ar' ? 'بحث...' : 'Search...'}
                  className={`pl-10 pr-4 py-2 border border-gray-300 rounded-lg ${isRTL ? 'text-right' : 'text-left'}`}
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
            </div>
          </div>

          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b">
                  <th className={`py-3 px-4 ${isRTL ? 'text-right' : 'text-left'}`}>
                    {language === 'ar' ? 'النوع' : 'Type'}
                  </th>
                  <th className={`py-3 px-4 ${isRTL ? 'text-right' : 'text-left'}`}>
                    {language === 'ar' ? 'المحتوى' : 'Content'}
                  </th>
                  <th className={`py-3 px-4 ${isRTL ? 'text-right' : 'text-left'}`}>
                    {language === 'ar' ? 'السبب' : 'Reason'}
                  </th>
                  <th className={`py-3 px-4 ${isRTL ? 'text-right' : 'text-left'}`}>
                    {language === 'ar' ? 'الخطورة' : 'Severity'}
                  </th>
                  <th className={`py-3 px-4 ${isRTL ? 'text-right' : 'text-left'}`}>
                    {language === 'ar' ? 'الحالة' : 'Status'}
                  </th>
                  <th className={`py-3 px-4 ${isRTL ? 'text-right' : 'text-left'}`}>
                    {language === 'ar' ? 'إجراءات' : 'Actions'}
                  </th>
                </tr>
              </thead>
              <tbody>
                {reports.map((report) => (
                  <tr key={report.id} className="border-b hover:bg-gray-50">
                    <td className="py-3 px-4">
                      <div className={`flex items-center gap-2 ${isRTL ? 'flex-row-reverse' : ''}`}>
                        {getTypeIcon(report.type)}
                        <span className="capitalize">{report.type}</span>
                      </div>
                    </td>
                    <td className="py-3 px-4">
                      <ArabicTypography variant="body2" className="truncate max-w-xs">
                        {report.content}
                      </ArabicTypography>
                      <ArabicTypography variant="body2" className="text-gray-500 text-xs">
                        {language === 'ar' ? 'بواسطة' : 'by'} {report.reportedUser}
                      </ArabicTypography>
                    </td>
                    <td className="py-3 px-4">
                      <ArabicTypography variant="body2">
                        {report.reason}
                      </ArabicTypography>
                    </td>
                    <td className="py-3 px-4">
                      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border ${getSeverityColor(report.severity)}`}>
                        {language === 'ar' 
                          ? (report.severity === 'critical' ? 'حرج' : 
                             report.severity === 'high' ? 'عالي' : 
                             report.severity === 'medium' ? 'متوسط' : 'منخفض')
                          : report.severity
                        }
                      </span>
                    </td>
                    <td className="py-3 px-4">
                      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(report.status)}`}>
                        {language === 'ar' 
                          ? (report.status === 'pending' ? 'معلق' : 
                             report.status === 'reviewing' ? 'قيد المراجعة' : 
                             report.status === 'resolved' ? 'تم الحل' : 'مرفوض')
                          : report.status
                        }
                      </span>
                    </td>
                    <td className="py-3 px-4">
                      <div className={`flex gap-1 ${isRTL ? 'flex-row-reverse' : ''}`}>
                        <ArabicButton size="sm" variant="outline" icon={<Eye className="w-3 h-3" />}>
                          {language === 'ar' ? 'عرض' : 'View'}
                        </ArabicButton>
                        {report.status === 'pending' && (
                          <ArabicButton size="sm" icon={<CheckCircle className="w-3 h-3" />}>
                            {language === 'ar' ? 'مراجعة' : 'Review'}
                          </ArabicButton>
                        )}
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </ArabicCard>
    </div>
  );
};

export default ModerationDashboardPage;
