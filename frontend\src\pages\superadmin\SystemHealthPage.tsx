import React, { useEffect } from 'react';

const SystemHealthPage: React.FC = () => {
  console.log('🚀 SystemHealthPage component is rendering!');
  console.log('🚀 SystemHealthPage: Component mounted at', new Date().toISOString());

  useEffect(() => {
    console.log('🚀 SystemHealthPage: useEffect triggered - component is fully mounted!');

    // Change document title to verify the page is active
    document.title = 'System Health - Yasmeen AI';

    // Add a visible indicator to the page
    const indicator = document.createElement('div');
    indicator.id = 'system-health-indicator';
    indicator.style.cssText = `
      position: fixed;
      top: 10px;
      right: 10px;
      background: #10b981;
      color: white;
      padding: 8px 16px;
      border-radius: 4px;
      z-index: 9999;
      font-weight: bold;
    `;
    indicator.textContent = '✅ SystemHealthPage Active!';
    document.body.appendChild(indicator);

    return () => {
      console.log('🚀 SystemHealthPage: Component unmounting');
      const existingIndicator = document.getElementById('system-health-indicator');
      if (existingIndicator) {
        existingIndicator.remove();
      }
    };
  }, []);

  return (
    <div className="p-6">
      <h1 className="text-2xl font-bold mb-4">🏥 System Health Dashboard</h1>
      <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-4">
        <h2 className="text-lg font-semibold text-green-800 mb-2">✅ Component Status</h2>
        <p className="text-green-700">SystemHealthPage is successfully rendering!</p>
        <p className="text-green-600 text-sm">Rendered at: {new Date().toLocaleString()}</p>
      </div>

      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
        <h2 className="text-lg font-semibold text-blue-800 mb-2">🔍 Debug Information</h2>
        <ul className="text-blue-700 space-y-1">
          <li>• Component: SystemHealthPage.tsx</li>
          <li>• Route: /super_admin/system-health</li>
          <li>• Import Path: ../pages/superadmin/SystemHealthPage</li>
          <li>• Status: ✅ Working correctly</li>
        </ul>
      </div>

      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
        <h2 className="text-lg font-semibold text-yellow-800 mb-2">⚡ Next Steps</h2>
        <p className="text-yellow-700">
          The SystemHealthPage component is now working! You can now implement the actual system health monitoring features.
        </p>
      </div>
    </div>
  );
};

export default SystemHealthPage;