/**
 * 🎯 CONSOLIDATED APP ROUTES
 * Simplified, maintainable routing system with unified role-based access control
 * 
 * CONSOLIDATION BENEFITS:
 * - Single routing component instead of multiple complex routers
 * - Unified role validation logic
 * - Cleaner route definitions
 * - Easier to maintain and extend
 * - Eliminates duplicate routing logic
 */

import React, { Suspense } from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { useRoles } from '../hooks/useRoles';

// Layout components
import Layout from '../components/layout/Layout';
import UserLayout from '../components/layout/UserLayout';

// Public pages
import HomePage from '../pages/HomePage';
import { FeaturesPage } from '../pages/FeaturesPage';
import LoginPage from '../pages/LoginPage';
import EnhancedRegisterPage from '../pages/EnhancedRegisterPage';
import RegistrationSuccessPage from '../pages/RegistrationSuccessPage';
import LogoutPage from '../pages/LogoutPage';
import AccessDeniedPage from '../pages/AccessDeniedPage';

// Protected pages
import AIChatPage from '../pages/ai/AIChatPage';
import UserProfilePage from '../pages/UserProfilePage';
import SettingsPage from '../pages/SettingsPage';
import CommunityPage from '../pages/CommunityPage';

// Debug components
import RoleTestingPanel from '../components/debug/RoleTestingPanel';

// Dynamic page routers - consolidated
import {
  DashboardRouter,
  HomePageRouter,
  AnalyticsPageRouter,
  UserManagementPageRouter,
  ContentManagementPageRouter,
  ApprovalsPageRouter,
  SystemPageRouter
} from './ConsolidatedPageRouter';

// Consolidated route protection
import { ConsolidatedProtectedRoute } from './ConsolidatedProtectedRoute';

// ========================================
// LOADING COMPONENT
// ========================================

const LoadingFallback = () => (
  <div className="flex items-center justify-center min-h-screen">
    <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-blue-500"></div>
  </div>
);

// ========================================
// DASHBOARD REDIRECT COMPONENT
// ========================================

const DashboardRedirect = () => {
  const { primaryRole, isLoading } = useRoles();

  if (isLoading) {
    return <LoadingFallback />;
  }

  console.log('🔄 DashboardRedirect - Primary Role:', primaryRole);

  // Regular users go to home page, others go to dashboard
  if (primaryRole === 'user') {
    console.log('✅ Redirecting user to /user/home');
    return <Navigate to="/user/home" replace />;
  }

  // Redirect to role-specific dashboard for business roles
  const dashboardPath = `/${primaryRole || 'entrepreneur'}/dashboard`;
  console.log('✅ Redirecting business role to:', dashboardPath);
  return <Navigate to={dashboardPath} replace />;
};

// ========================================
// ROLE-BASED PAGE COMPONENT
// ========================================

interface RoleBasedPageProps {
  children: React.ReactNode;
  allowedRoles?: string[];
}

const RoleBasedPage: React.FC<RoleBasedPageProps> = ({
  children,
  allowedRoles = ['entrepreneur', 'mentor', 'investor', 'moderator', 'admin', 'super_admin'] // REMOVED 'user' from default
}) => {
  return (
    <ConsolidatedProtectedRoute allowedRoles={allowedRoles}>
      {children}
    </ConsolidatedProtectedRoute>
  );
};

// ========================================
// MAIN APP ROUTES COMPONENT
// ========================================

const ConsolidatedAppRoutes: React.FC = () => {
  return (
    <Suspense fallback={<LoadingFallback />}>
      <Routes>
        {/* ========================================
            PUBLIC ROUTES
        ======================================== */}
        
        <Route path="/" element={<HomePage />} />
        <Route path="/features" element={<FeaturesPage />} />
        <Route path="/login" element={<LoginPage />} />
        <Route path="/register" element={<EnhancedRegisterPage />} />
        
        {/* Consolidated registration success routes */}
        <Route path="/registration-success" element={<RegistrationSuccessPage />} />
        <Route path="/register/success" element={<RegistrationSuccessPage />} />
        <Route path="/signup/success" element={<RegistrationSuccessPage />} />
        
        <Route path="/logout" element={<LogoutPage />} />
        <Route path="/access-denied" element={<AccessDeniedPage />} />
        
        {/* Public AI Chat access */}
        <Route path="/ai-chat" element={<AIChatPage />} />

        {/* Community page - accessible to all authenticated users */}
        <Route path="/community" element={
          <ConsolidatedProtectedRoute allowedRoles={['user', 'entrepreneur', 'mentor', 'investor', 'moderator', 'admin', 'super_admin']}>
            <CommunityPage />
          </ConsolidatedProtectedRoute>
        } />

        {/* Role Testing Page - For development/testing */}
        <Route path="/role-test" element={
          <div className="min-h-screen bg-gray-50 py-8">
            <div className="max-w-6xl mx-auto px-4">
              <RoleTestingPanel />
            </div>
          </div>
        } />

        {/* ========================================
            REGULAR USER ROUTES (SIMPLE LAYOUT)
        ======================================== */}

        <Route path="/user" element={<UserLayout />}>
          {/* Home routes for regular users */}
          <Route path="home" element={
            <ConsolidatedProtectedRoute allowedRoles={['user']}>
              <HomePageRouter />
            </ConsolidatedProtectedRoute>
          } />

          {/* Basic profile for regular users */}
          <Route path="profile" element={
            <ConsolidatedProtectedRoute allowedRoles={['user']}>
              <UserProfilePage />
            </ConsolidatedProtectedRoute>
          } />

          {/* Basic settings for regular users */}
          <Route path="settings" element={
            <ConsolidatedProtectedRoute allowedRoles={['user']}>
              <SettingsPage />
            </ConsolidatedProtectedRoute>
          } />
        </Route>

        {/* Dashboard redirect for authenticated users - MOVED OUTSIDE LAYOUT */}
        <Route path="/dashboard" element={<DashboardRedirect />} />

        {/* ========================================
            PROTECTED ROUTES WITH SIDEBAR LAYOUT
        ======================================== */}

        <Route path="/" element={<Layout />}>

          {/* Dashboard routes - EXCLUDED regular users */}
          <Route path="/:role/dashboard" element={
            <RoleBasedPage allowedRoles={['entrepreneur', 'mentor', 'investor', 'moderator', 'admin', 'super_admin']}>
              <DashboardRouter />
            </RoleBasedPage>
          } />
          
          {/* Profile routes - EXCLUDED regular users */}
          <Route path="/:role/profile" element={
            <RoleBasedPage allowedRoles={['entrepreneur', 'mentor', 'investor', 'moderator', 'admin', 'super_admin']}>
              <UserProfilePage />
            </RoleBasedPage>
          } />

          {/* Settings routes - EXCLUDED regular users */}
          <Route path="/:role/settings" element={
            <RoleBasedPage allowedRoles={['entrepreneur', 'mentor', 'investor', 'moderator', 'admin', 'super_admin']}>
              <SettingsPage />
            </RoleBasedPage>
          } />

          {/* AI Chat routes - EXCLUDED regular users */}
          <Route path="/:role/ai-chat" element={
            <RoleBasedPage allowedRoles={['entrepreneur', 'mentor', 'investor', 'moderator', 'admin', 'super_admin']}>
              <AIChatPage />
            </RoleBasedPage>
          } />
          
          {/* ========================================
              ADMIN AND MANAGEMENT ROUTES
          ======================================== */}
          
          {/* User Management - Admin and Super Admin only */}
          <Route path="/:role/users" element={
            <RoleBasedPage allowedRoles={['admin', 'super_admin']}>
              <UserManagementPageRouter />
            </RoleBasedPage>
          } />

          {/* Content Management - Admin and Super Admin only */}
          <Route path="/:role/content" element={
            <RoleBasedPage allowedRoles={['admin', 'super_admin']}>
              <ContentManagementPageRouter />
            </RoleBasedPage>
          } />

          {/* Analytics - Admin and Super Admin only */}
          <Route path="/:role/analytics" element={
            <RoleBasedPage allowedRoles={['admin', 'super_admin']}>
              <AnalyticsPageRouter />
            </RoleBasedPage>
          } />

          {/* Approvals - Super Admin only */}
          <Route path="/:role/approvals" element={
            <RoleBasedPage allowedRoles={['super_admin']}>
              <ApprovalsPageRouter />
            </RoleBasedPage>
          } />

          {/* System Management - Super Admin only */}
          <Route path="/:role/system" element={
            <RoleBasedPage allowedRoles={['super_admin']}>
              <SystemPageRouter />
            </RoleBasedPage>
          } />
          
        </Route>

        {/* ========================================
            FALLBACK ROUTES
        ======================================== */}
        
        {/* Catch-all route for 404 */}
        <Route path="*" element={
          <div className="flex items-center justify-center min-h-screen">
            <div className="text-center">
              <h1 className="text-4xl font-bold text-gray-800 mb-4">404</h1>
              <p className="text-gray-600 mb-4">Page not found</p>
              <Navigate to="/" replace />
            </div>
          </div>
        } />
        
      </Routes>
    </Suspense>
  );
};

export default ConsolidatedAppRoutes;
