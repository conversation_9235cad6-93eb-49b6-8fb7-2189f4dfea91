/**
 * 🎯 CONSOLIDATED PAGE ROUTER
 * Simplified page routing logic that replaces complex PageRouters
 * 
 * CONSOLIDATION BENEFITS:
 * - Single page router instead of multiple complex routers
 * - Unified role-based page selection logic
 * - Cleaner component mapping
 * - Easier to maintain and extend
 * - Eliminates duplicate page routing logic
 */

import React, { useMemo } from 'react';
import { useParams } from 'react-router-dom';
import { useRoles } from '../hooks/useRoles';

// Import page components
import DashboardPage from '../pages/DashboardPage';
import UserHomePage from '../pages/UserHomePage';

// Analytics pages
import AdminAnalyticsPage from '../pages/admin/AdminAnalyticsPage';
import SuperAdminAnalyticsPage from '../pages/superadmin/SuperAdminAnalyticsPage';

// User management pages
import UserManagementPage from '../pages/admin/UserManagementPage';
import SuperAdminUserManagementPage from '../pages/superadmin/SuperAdminUserManagementPage';

// Content management pages
import ContentManagementPage from '../pages/admin/ContentManagementPage';
import SuperAdminContentPage from '../pages/superadmin/SuperAdminContentPage';

// Approval pages
import RoleApplicationsPage from '../pages/admin/RoleApplicationsPage';
import SuperAdminApprovalsPage from '../pages/superadmin/SuperAdminApprovalsPage';

// System pages
import SuperAdminSecurityPage from '../pages/superadmin/SuperAdminSecurityPage';

// ========================================
// INTERFACES
// ========================================

interface ConsolidatedPageRouterProps {
  pageType: 'dashboard' | 'home' | 'analytics' | 'users' | 'content' | 'approvals' | 'system';
}

interface RouteParams extends Record<string, string | undefined> {
  role: string;
}

// ========================================
// PAGE MAPPING CONFIGURATION
// ========================================

const PAGE_MAPPINGS = {
  dashboard: {
    // REMOVED 'user' - regular users should NOT have dashboard access
    entrepreneur: DashboardPage,
    mentor: DashboardPage,
    investor: DashboardPage,
    moderator: DashboardPage,
    admin: DashboardPage,
    super_admin: DashboardPage,
  },
  home: {
    // Regular users get a simple home page instead of dashboard
    user: UserHomePage,
  },
  analytics: {
    admin: AdminAnalyticsPage,
    super_admin: SuperAdminAnalyticsPage,
  },
  users: {
    admin: UserManagementPage,
    super_admin: SuperAdminUserManagementPage,
  },
  content: {
    admin: ContentManagementPage,
    super_admin: SuperAdminContentPage,
  },
  approvals: {
    admin: RoleApplicationsPage,
    super_admin: SuperAdminApprovalsPage,
  },
  system: {
    super_admin: SuperAdminSecurityPage,
  },
} as const;

// ========================================
// FALLBACK COMPONENTS
// ========================================

const AccessDeniedComponent = ({ pageType, role }: { pageType: string; role: string }) => (
  <div className="flex items-center justify-center min-h-screen">
    <div className="text-center">
      <h1 className="text-2xl font-bold text-gray-800 mb-4">Access Denied</h1>
      <p className="text-gray-600 mb-4">
        You don't have permission to access the {pageType} page as a {role}.
      </p>
      <p className="text-sm text-gray-500">
        Please contact your administrator if you believe this is an error.
      </p>
    </div>
  </div>
);

const PageNotFoundComponent = ({ pageType, role }: { pageType: string; role: string }) => (
  <div className="flex items-center justify-center min-h-screen">
    <div className="text-center">
      <h1 className="text-2xl font-bold text-gray-800 mb-4">Page Not Found</h1>
      <p className="text-gray-600 mb-4">
        The {pageType} page for {role} role is not yet implemented.
      </p>
      <p className="text-sm text-gray-500">
        This feature is coming soon.
      </p>
    </div>
  </div>
);

const LoadingComponent = () => (
  <div className="flex items-center justify-center min-h-screen">
    <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-blue-500"></div>
  </div>
);

// ========================================
// MAIN COMPONENT
// ========================================

export const ConsolidatedPageRouter: React.FC<ConsolidatedPageRouterProps> = ({ pageType }) => {
  const { role } = useParams<RouteParams>();
  const { isLoading } = useRoles();

  // Normalize role (handle variations like superadmin -> super_admin)
  const normalizedRole = useMemo(() => {
    if (!role) return 'user';
    return role === 'superadmin' ? 'super_admin' : role;
  }, [role]);

  // Get the appropriate page component
  const PageComponent = useMemo(() => {
    console.log('🔍 ConsolidatedPageRouter:', { pageType, role, normalizedRole, isLoading });

    const pageMapping = PAGE_MAPPINGS[pageType];
    if (!pageMapping) {
      console.log('❌ No page mapping found for:', pageType);
      return () => <PageNotFoundComponent pageType={pageType} role={normalizedRole} />;
    }

    const Component = pageMapping[normalizedRole as keyof typeof pageMapping];
    if (!Component) {
      console.log('❌ No component found for:', { pageType, normalizedRole });
      return () => <AccessDeniedComponent pageType={pageType} role={normalizedRole} />;
    }

    console.log('✅ Found component for:', { pageType, normalizedRole, Component: Component?.name || 'Unknown' });
    return Component;
  }, [pageType, normalizedRole]);

  // Show loading while checking roles
  if (isLoading) {
    console.log('⏳ ConsolidatedPageRouter still loading roles...');
    return <LoadingComponent />;
  }

  console.log('🎯 Rendering PageComponent:', PageComponent.name);
  // Render the appropriate page component
  return <PageComponent />;
};

// ========================================
// CONVENIENCE COMPONENTS FOR SPECIFIC PAGES
// ========================================

export const DashboardRouter: React.FC = () => (
  <ConsolidatedPageRouter pageType="dashboard" />
);

export const HomePageRouter: React.FC = () => {
  console.log('🏠 HomePageRouter rendering...');
  return <ConsolidatedPageRouter pageType="home" />;
};

export const AnalyticsPageRouter: React.FC = () => (
  <ConsolidatedPageRouter pageType="analytics" />
);

export const UserManagementPageRouter: React.FC = () => (
  <ConsolidatedPageRouter pageType="users" />
);

export const ContentManagementPageRouter: React.FC = () => (
  <ConsolidatedPageRouter pageType="content" />
);

export const ApprovalsPageRouter: React.FC = () => (
  <ConsolidatedPageRouter pageType="approvals" />
);

export const SystemPageRouter: React.FC = () => (
  <ConsolidatedPageRouter pageType="system" />
);

export default ConsolidatedPageRouter;
