/**
 * 🎯 CONSOLIDATED PROTECTED ROUTE
 * Simplified route protection with unified role validation
 * 
 * CONSOLIDATION BENEFITS:
 * - Single route protection component instead of multiple complex ones
 * - Unified role validation logic
 * - Cleaner access control
 * - Easier to maintain and debug
 * - Eliminates duplicate protection logic
 */

import React, { useEffect, useState } from 'react';
import { useParams, Navigate, useLocation } from 'react-router-dom';
import { useAuth } from '../hooks/useAuth';
import { useRoles } from '../hooks/useRoles';

// ========================================
// INTERFACES
// ========================================

interface ConsolidatedProtectedRouteProps {
  children: React.ReactNode;
  allowedRoles?: string[];
  requireAll?: boolean;
  fallbackPath?: string;
}

interface RouteParams extends Record<string, string | undefined> {
  role?: string;
}

// ========================================
// VALID ROLES CONFIGURATION
// ========================================

// ========================================
// LOADING COMPONENT
// ========================================

const LoadingFallback = () => (
  <div className="flex items-center justify-center min-h-screen">
    <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-blue-500"></div>
  </div>
);

// ========================================
// MAIN COMPONENT
// ========================================

export const ConsolidatedProtectedRoute: React.FC<ConsolidatedProtectedRouteProps> = ({
  children,
  allowedRoles = ['user', 'entrepreneur', 'mentor', 'investor', 'moderator', 'admin', 'super_admin'],
  requireAll = false,
  fallbackPath = '/access-denied'
}) => {
  // ========================================
  // HOOKS AND STATE
  // ========================================
  
  const { isAuthenticated, isLoading: authLoading } = useAuth();
  const { hasRole, primaryRole, isLoading: roleLoading } = useRoles();
  const { role: urlRole } = useParams<RouteParams>();
  const location = useLocation();
  
  const [hasAccess, setHasAccess] = useState<boolean>(false);
  const [checkingAccess, setCheckingAccess] = useState<boolean>(true);
  const [redirectPath, setRedirectPath] = useState<string | null>(null);

  // ========================================
  // ROLE VALIDATION LOGIC
  // ========================================

  const normalizeRole = (role: string): string => {
    // Handle role variations
    if (role === 'superadmin') return 'super_admin';
    return role;
  };

  // ========================================
  // ACCESS CHECKING EFFECT
  // ========================================

  useEffect(() => {
    const checkAccess = async () => {
      // Don't check access while authentication or roles are loading
      if (authLoading || roleLoading) {
        return;
      }

      // Wait for primary role to be loaded
      if (!primaryRole) {
        console.log('⏳ Primary role not yet loaded...');
        return;
      }

      setCheckingAccess(true);

      try {
        console.log('🔍 Checking access:', {
          isAuthenticated,
          urlRole,
          allowedRoles,
          primaryRole,
          pathname: location.pathname
        });

        // Check authentication first
        if (!isAuthenticated) {
          console.log('❌ User not authenticated, redirecting to login');
          setRedirectPath(`/login?redirect=${encodeURIComponent(location.pathname)}`);
          setHasAccess(false);
          setCheckingAccess(false);
          return;
        }

        // If URL contains a role parameter, validate it
        if (urlRole) {
          const normalizedUrlRole = normalizeRole(urlRole);
          const validRoles = ['user', 'entrepreneur', 'mentor', 'investor', 'moderator', 'admin', 'super_admin'];

          // Check if URL role is valid
          if (!validRoles.includes(normalizedUrlRole)) {
            console.log(`❌ Invalid role in URL: ${urlRole}`);
            // FIXED: Regular users go to home, others go to dashboard
            const redirectTo = primaryRole === 'user' ? '/user/home' : `/${primaryRole}/dashboard`;
            setRedirectPath(redirectTo);
            setHasAccess(false);
            setCheckingAccess(false);
            return;
          }

          // Check if user has the URL role
          const userHasUrlRole = await hasRole(normalizedUrlRole);
          if (!userHasUrlRole) {
            console.log(`❌ User doesn't have role: ${normalizedUrlRole}`);
            // FIXED: Regular users go to home, others go to dashboard
            const redirectTo = primaryRole === 'user' ? '/user/home' : `/${primaryRole}/dashboard`;
            setRedirectPath(redirectTo);
            setHasAccess(false);
            setCheckingAccess(false);
            return;
          }
        }

        // Check role-based access permissions
        let accessGranted = false;

        if (allowedRoles.length > 0) {
          // Simple role check using primaryRole instead of async hasRole calls
          if (requireAll) {
            // User must have ALL specified roles (for this simple case, check if primaryRole is in allowedRoles)
            accessGranted = allowedRoles.includes(primaryRole);
          } else {
            // User must have ANY of the specified roles
            accessGranted = allowedRoles.includes(primaryRole);
          }

          console.log('🔍 Role check result:', {
            allowedRoles,
            primaryRole,
            requireAll,
            accessGranted
          });
        } else {
          // If no specific roles required, allow authenticated users
          accessGranted = true;
        }

        if (!accessGranted) {
          console.log(`❌ Access denied. Required roles: ${allowedRoles.join(', ')}, User role: ${primaryRole}`);
          // FIXED: Better fallback logic for regular users
          if (primaryRole === 'user') {
            setRedirectPath('/user/home');
          } else {
            setRedirectPath(fallbackPath);
          }
          setHasAccess(false);
        } else {
          console.log(`✅ Access granted for roles: ${allowedRoles.join(', ')}`);
          setHasAccess(true);
        }

      } catch (error) {
        console.error('❌ Error checking route access:', error);
        // FIXED: Better error fallback for regular users
        if (primaryRole === 'user') {
          setRedirectPath('/user/home');
        } else {
          setRedirectPath(fallbackPath);
        }
        setHasAccess(false);
      } finally {
        setCheckingAccess(false);
      }
    };

    checkAccess();
  }, [
    isAuthenticated,
    authLoading,
    roleLoading,
    urlRole,
    JSON.stringify(allowedRoles), // Stringify to prevent array reference changes
    requireAll,
    fallbackPath,
    location.pathname,
    primaryRole
    // REMOVED: hasRole, hasAnyRole, hasAllRoles to prevent infinite loops
  ]);

  // ========================================
  // RENDER LOGIC
  // ========================================

  console.log('🎯 ConsolidatedProtectedRoute render:', {
    authLoading,
    roleLoading,
    checkingAccess,
    hasAccess,
    redirectPath,
    childrenCount: React.Children.count(children)
  });

  // Show loading while checking authentication or access
  if (authLoading || roleLoading || checkingAccess) {
    console.log('⏳ Showing LoadingFallback...');
    return <LoadingFallback />;
  }

  // Redirect if access denied or invalid role
  if (redirectPath) {
    console.log('🔄 Redirecting to:', redirectPath);
    return <Navigate to={redirectPath} replace />;
  }

  // Render children if access granted
  if (hasAccess) {
    console.log('✅ Rendering children...');
    return <>{children}</>;
  }

  // Default fallback
  console.log('❌ Default fallback redirect to:', fallbackPath);
  return <Navigate to={fallbackPath} replace />;
};

// ========================================
// CONVENIENCE COMPONENTS
// ========================================

// Super Admin only routes
export const SuperAdminRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <ConsolidatedProtectedRoute allowedRoles={['super_admin']}>
    {children}
  </ConsolidatedProtectedRoute>
);

// Admin routes (admin or super_admin)
export const AdminRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <ConsolidatedProtectedRoute allowedRoles={['admin', 'super_admin']}>
    {children}
  </ConsolidatedProtectedRoute>
);

// Business routes (entrepreneur, mentor, investor)
export const BusinessRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <ConsolidatedProtectedRoute allowedRoles={['entrepreneur', 'mentor', 'investor', 'admin', 'super_admin']}>
    {children}
  </ConsolidatedProtectedRoute>
);

// Any authenticated user
export const AuthenticatedRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <ConsolidatedProtectedRoute allowedRoles={['user', 'entrepreneur', 'mentor', 'investor', 'moderator', 'admin', 'super_admin']}>
    {children}
  </ConsolidatedProtectedRoute>
);

export default ConsolidatedProtectedRoute;
