/**
 * Role Application API Service
 * Handles all role application related API calls
 */

import { api } from './api';

export interface RoleApplication {
  id: string;
  user: {
    id: string;
    username: string;
    email: string;
    first_name: string;
    last_name: string;
  };
  requested_role: {
    id: string;
    name: string;
    display_name: string;
    description: string;
  };
  motivation: string;
  qualifications: string;
  experience?: string;
  portfolio_url?: string;
  status: 'pending' | 'approved' | 'rejected' | 'withdrawn';
  reviewed_by?: {
    username: string;
    first_name: string;
    last_name: string;
  };
  reviewed_at?: string;
  admin_notes?: string;
  created_at: string;
  
  // Role-specific fields
  // Entrepreneur fields
  company_name?: string;
  project_stage?: string;
  industry?: string;
  project_description?: string;
  funding_needed?: string;
  team_size?: string;
  support_needed?: string;
  previous_experience?: string;
  
  // Mentor fields
  expertise_areas?: string;
  mentoring_experience?: string;
  availability?: string;
  preferred_communication?: string;
  
  // Investor fields
  investment_range?: string;
  investment_focus?: string;
  investment_stage?: string;
  portfolio_companies?: string;
  due_diligence_requirements?: string;
  
  // Community Member fields
  interests?: string;
  goals?: string;
}

export interface CreateRoleApplicationData {
  requested_role: string; // Role ID
  motivation: string;
  qualifications: string;
  experience?: string;
  portfolio_url?: string;
  
  // Role-specific fields (optional based on role)
  company_name?: string;
  project_stage?: string;
  industry?: string;
  project_description?: string;
  funding_needed?: string;
  team_size?: string;
  support_needed?: string;
  previous_experience?: string;
  expertise_areas?: string;
  mentoring_experience?: string;
  availability?: string;
  preferred_communication?: string;
  investment_range?: string;
  investment_focus?: string;
  investment_stage?: string;
  portfolio_companies?: string;
  due_diligence_requirements?: string;
  interests?: string;
  goals?: string;
}

export interface RoleApplicationStatistics {
  total_applications: number;
  pending_applications: number;
  approved_applications: number;
  rejected_applications: number;
  by_role: Record<string, {
    total: number;
    pending: number;
    approved: number;
    rejected: number;
  }>;
}

class RoleApplicationAPI {
  private baseUrl = '/api/users/role-applications';

  /**
   * Get all role applications (admin only)
   */
  async getAllApplications(): Promise<RoleApplication[]> {
    return api.get(`${this.baseUrl}/`);
  }

  /**
   * Get current user's role applications
   */
  async getMyApplications(): Promise<{
    success: boolean;
    applications: RoleApplication[];
    count: number;
  }> {
    return api.get(`${this.baseUrl}/my_applications/`);
  }

  /**
   * Get pending role applications (admin only)
   */
  async getPendingApplications(): Promise<{
    success: boolean;
    applications: RoleApplication[];
    count: number;
  }> {
    return api.get(`${this.baseUrl}/pending_applications/`);
  }

  /**
   * Get specific role application details
   */
  async getApplication(id: string): Promise<RoleApplication> {
    return api.get(`${this.baseUrl}/${id}/`);
  }

  /**
   * Submit a new role application
   */
  async createApplication(data: CreateRoleApplicationData): Promise<RoleApplication> {
    return api.post(`${this.baseUrl}/`, data);
  }

  /**
   * Approve a role application (admin only)
   */
  async approveApplication(id: string): Promise<{
    success: boolean;
    message: string;
    application: RoleApplication;
  }> {
    return api.post(`${this.baseUrl}/${id}/approve/`);
  }

  /**
   * Reject a role application (admin only)
   */
  async rejectApplication(id: string, reason?: string): Promise<{
    success: boolean;
    message: string;
    application: RoleApplication;
  }> {
    return api.post(`${this.baseUrl}/${id}/reject/`, {
      reason: reason || 'No reason provided'
    });
  }

  /**
   * Get role application statistics (admin only)
   */
  async getStatistics(): Promise<{
    success: boolean;
    statistics: RoleApplicationStatistics;
  }> {
    return api.get(`${this.baseUrl}/statistics/`);
  }

  /**
   * Update a role application (only if pending)
   */
  async updateApplication(id: string, data: Partial<CreateRoleApplicationData>): Promise<RoleApplication> {
    return api.patch(`${this.baseUrl}/${id}/`, data);
  }

  /**
   * Withdraw a role application
   */
  async withdrawApplication(id: string): Promise<RoleApplication> {
    return api.patch(`${this.baseUrl}/${id}/`, {
      status: 'withdrawn'
    });
  }

  /**
   * Get applications by role type (admin only)
   */
  async getApplicationsByRole(roleName: string): Promise<RoleApplication[]> {
    return api.get(`${this.baseUrl}/?requested_role__name=${roleName}`);
  }

  /**
   * Get applications by status (admin only)
   */
  async getApplicationsByStatus(status: string): Promise<RoleApplication[]> {
    return api.get(`${this.baseUrl}/?status=${status}`);
  }
}

export const roleApplicationAPI = new RoleApplicationAPI();
export default roleApplicationAPI;
