/**
 * Role Debugger Utility
 * Debug role validation issues and authentication problems
 */

import { unifiedRoleService } from '../services/roleApi';

export interface RoleDebugInfo {
  isAuthenticated: boolean;
  currentUser: any;
  primaryRole: string;
  allRoles: string[];
  permissionLevel: string;
  error?: string;
}

export class RoleDebugger {
  /**
   * Get comprehensive role debug information
   */
  static async getDebugInfo(): Promise<RoleDebugInfo> {
    try {
      console.log('🔍 Starting role debug analysis...');
      
      // Check authentication status
      const token = localStorage.getItem('authToken');
      const isAuthenticated = !!token;
      
      console.log(`🔍 Authentication status: ${isAuthenticated ? 'Authenticated' : 'Not authenticated'}`);
      console.log(`🔍 Token exists: ${!!token}`);
      
      if (!isAuthenticated) {
        return {
          isAuthenticated: false,
          currentUser: null,
          primaryRole: 'anonymous',
          allRoles: ['anonymous'],
          permissionLevel: 'none',
          error: 'User not authenticated'
        };
      }
      
      // Get user role information
      const userInfo = await unifiedRoleService.getCurrentUserRoles();
      console.log('🔍 User role info:', userInfo);
      
      return {
        isAuthenticated: true,
        currentUser: userInfo,
        primaryRole: userInfo.primary_role,
        allRoles: userInfo.all_roles.map((r: any) => r.name),
        permissionLevel: userInfo.permission_level,
      };
      
    } catch (error) {
      console.error('❌ Role debug error:', error);
      return {
        isAuthenticated: false,
        currentUser: null,
        primaryRole: 'unknown',
        allRoles: [],
        permissionLevel: 'none',
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }
  
  /**
   * Test role validation for a specific role
   */
  static async testRoleValidation(roleName: string): Promise<boolean> {
    try {
      console.log(`🧪 Testing role validation for: ${roleName}`);
      
      const debugInfo = await this.getDebugInfo();
      console.log('🔍 Debug info:', debugInfo);
      
      if (!debugInfo.isAuthenticated) {
        console.log('❌ User not authenticated, role validation failed');
        return false;
      }
      
      const hasRole = await unifiedRoleService.hasRole(roleName);
      console.log(`🔍 hasRole('${roleName}') result: ${hasRole}`);
      
      return hasRole;
      
    } catch (error) {
      console.error(`❌ Role validation test failed for '${roleName}':`, error);
      return false;
    }
  }
  
  /**
   * Test invalid role handling
   */
  static async testInvalidRoleHandling(invalidRole: string): Promise<void> {
    console.log(`🧪 Testing invalid role handling for: ${invalidRole}`);
    
    const validRoles = ['user', 'entrepreneur', 'mentor', 'investor', 'moderator', 'admin', 'super_admin'];
    const isValidRole = validRoles.includes(invalidRole);
    
    console.log(`🔍 Is '${invalidRole}' a valid role? ${isValidRole ? 'Yes' : 'No'}`);
    console.log(`🔍 Valid roles are: ${validRoles.join(', ')}`);
    
    if (!isValidRole) {
      console.log(`✅ Invalid role '${invalidRole}' correctly identified as invalid`);
      console.log(`🔄 Should redirect to fallback role (usually 'user')`);
    } else {
      console.log(`⚠️ Role '${invalidRole}' is actually valid, testing access...`);
      await this.testRoleValidation(invalidRole);
    }
  }
}

// Export for global debugging
(window as any).RoleDebugger = RoleDebugger;

export default RoleDebugger;
