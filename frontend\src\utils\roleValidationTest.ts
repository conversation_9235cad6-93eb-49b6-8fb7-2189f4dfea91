/**
 * Role Validation Test Utility
 * Test role validation fixes and error handling
 */

import { unifiedRoleService } from '../services/roleApi';

export interface RoleValidationTestResult {
  testName: string;
  passed: boolean;
  error?: string;
  details?: any;
}

export class RoleValidationTester {
  private results: RoleValidationTestResult[] = [];

  /**
   * Run all role validation tests
   */
  async runAllTests(): Promise<RoleValidationTestResult[]> {
    this.results = [];
    
    console.log('🧪 Starting Role Validation Tests...');
    
    await this.testBasicRoleChecking();
    await this.testErrorHandling();
    await this.testPermissionValidation();
    await this.testRoleHierarchy();
    
    console.log('✅ Role Validation Tests Complete');
    return this.results;
  }

  /**
   * Test basic role checking functionality
   */
  private async testBasicRoleChecking(): Promise<void> {
    try {
      // Test valid role check
      const hasUserRole = await unifiedRoleService.hasRole('user');
      this.addResult('Basic Role Check - User', typeof hasUserRole === 'boolean', undefined, { hasUserRole });

      // Test invalid role check
      const hasInvalidRole = await unifiedRoleService.hasRole('invalid_role');
      this.addResult('Basic Role Check - Invalid', typeof hasInvalidRole === 'boolean', undefined, { hasInvalidRole });

      // Test multiple role check
      const hasAnyRole = await unifiedRoleService.hasAnyRole(['user', 'admin']);
      this.addResult('Multiple Role Check', typeof hasAnyRole === 'boolean', undefined, { hasAnyRole });

    } catch (error) {
      this.addResult('Basic Role Check', false, error instanceof Error ? error.message : 'Unknown error');
    }
  }

  /**
   * Test error handling in role validation
   */
  private async testErrorHandling(): Promise<void> {
    try {
      // Test with empty role name
      try {
        await unifiedRoleService.hasRole('');
        this.addResult('Error Handling - Empty Role', false, 'Should have thrown error for empty role');
      } catch (error) {
        this.addResult('Error Handling - Empty Role', true, undefined, { errorMessage: error instanceof Error ? error.message : 'Unknown error' });
      }

      // Test with null role name
      try {
        await unifiedRoleService.hasRole(null as any);
        this.addResult('Error Handling - Null Role', false, 'Should have thrown error for null role');
      } catch (error) {
        this.addResult('Error Handling - Null Role', true, undefined, { errorMessage: error instanceof Error ? error.message : 'Unknown error' });
      }

    } catch (error) {
      this.addResult('Error Handling Tests', false, error instanceof Error ? error.message : 'Unknown error');
    }
  }

  /**
   * Test permission validation
   */
  private async testPermissionValidation(): Promise<void> {
    try {
      // Test permission check
      const hasReadPermission = await unifiedRoleService.hasPermission('read');
      this.addResult('Permission Check - Read', typeof hasReadPermission === 'boolean', undefined, { hasReadPermission });

      // Test admin permission
      const hasAdminPermission = await unifiedRoleService.hasPermission('admin');
      this.addResult('Permission Check - Admin', typeof hasAdminPermission === 'boolean', undefined, { hasAdminPermission });

    } catch (error) {
      this.addResult('Permission Validation', false, error instanceof Error ? error.message : 'Unknown error');
    }
  }

  /**
   * Test role hierarchy validation
   */
  private async testRoleHierarchy(): Promise<void> {
    try {
      // Test admin role check
      const isAdmin = await unifiedRoleService.isAdmin();
      this.addResult('Role Hierarchy - Admin', typeof isAdmin === 'boolean', undefined, { isAdmin });

      // Test super admin role check
      const isSuperAdmin = await unifiedRoleService.isSuperAdmin();
      this.addResult('Role Hierarchy - Super Admin', typeof isSuperAdmin === 'boolean', undefined, { isSuperAdmin });

    } catch (error) {
      this.addResult('Role Hierarchy Tests', false, error instanceof Error ? error.message : 'Unknown error');
    }
  }

  /**
   * Add test result
   */
  private addResult(testName: string, passed: boolean, error?: string, details?: any): void {
    const result: RoleValidationTestResult = {
      testName,
      passed,
      error,
      details
    };
    
    this.results.push(result);
    
    const status = passed ? '✅' : '❌';
    console.log(`${status} ${testName}${error ? ` - ${error}` : ''}`);
  }

  /**
   * Get test summary
   */
  getTestSummary(): { total: number; passed: number; failed: number; passRate: number } {
    const total = this.results.length;
    const passed = this.results.filter(r => r.passed).length;
    const failed = total - passed;
    const passRate = total > 0 ? (passed / total) * 100 : 0;

    return { total, passed, failed, passRate };
  }

  /**
   * Print test summary
   */
  printSummary(): void {
    const summary = this.getTestSummary();
    console.log('\n📊 Role Validation Test Summary:');
    console.log(`Total Tests: ${summary.total}`);
    console.log(`Passed: ${summary.passed}`);
    console.log(`Failed: ${summary.failed}`);
    console.log(`Pass Rate: ${summary.passRate.toFixed(1)}%`);
    
    if (summary.failed > 0) {
      console.log('\n❌ Failed Tests:');
      this.results.filter(r => !r.passed).forEach(result => {
        console.log(`  - ${result.testName}: ${result.error}`);
      });
    }
  }
}

/**
 * Run role validation tests
 */
export const runRoleValidationTests = async (): Promise<RoleValidationTestResult[]> => {
  const tester = new RoleValidationTester();
  const results = await tester.runAllTests();
  tester.printSummary();
  return results;
};

/**
 * Quick role validation check for debugging
 */
export const quickRoleCheck = async (roleName: string): Promise<void> => {
  try {
    console.log(`🔍 Quick Role Check: ${roleName}`);
    const hasRole = await unifiedRoleService.hasRole(roleName);
    console.log(`Result: ${hasRole ? '✅ Has Role' : '❌ No Role'}`);
    
    const userInfo = await unifiedRoleService.getCurrentUserRoles();
    console.log('User Info:', {
      primaryRole: userInfo.primary_role,
      allRoles: userInfo.all_roles.map(r => r.name),
      permissionLevel: userInfo.permission_level
    });
  } catch (error) {
    console.error('❌ Quick Role Check Failed:', error);
  }
};

export default RoleValidationTester;
